import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';

export interface CompanySettings {
  name: string;
  address: string;
  phone: string;
  email: string;
  gstin: string;
  pan?: string;
  stateCode?: string;
  website?: string;
  bankDetails?: {
    bankName: string;
    accountNumber: string;
    ifscCode: string;
    branch: string;
  };
}

// API function to fetch company settings
async function fetchCompanySettings() {
  const response = await apiRequest("GET", "/api/settings?category=company");
  return response.json();
}

// Helper function to get setting value from database with proper JSON parsing
const getSettingValue = (settings: any[], key: string, defaultValue: string = "") => {
  // Handle key mapping for legacy database entries
  const keyMap: { [key: string]: string } = {
    'gstin': 'gst_number', // Map gstin to legacy gst_number key
  };
  
  const dbKey = keyMap[key] || key;
  const setting = settings?.find((s: any) => s.key === dbKey || s.key === key);
  if (!setting) return defaultValue;

  let value = setting.value;
  // Handle JSON-encoded strings
  if (typeof value === 'string' && (value.startsWith('"') || value.startsWith('{'))) {
    try {
      value = JSON.parse(value);
    } catch {
      // If parsing fails, use the string as-is
    }
  }
  return value || defaultValue;
};

// Custom hook to get company settings from database
export const useCompanySettings = () => {
  const { data: rawSettings, isLoading, error } = useQuery({
    queryKey: ["/api/settings", "company"],
    queryFn: fetchCompanySettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
  });

  // Transform raw settings into structured company settings
  const companySettings: CompanySettings = {
    name: getSettingValue(rawSettings, "name", "Shree Jewelry House"),
    address: getSettingValue(rawSettings, "address", "123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu"),
    phone: getSettingValue(rawSettings, "phone", "+91 98765 43210"),
    email: getSettingValue(rawSettings, "email", "<EMAIL>"),
    gstin: getSettingValue(rawSettings, "gstin", "33AAAAA0000A1Z5"), // This will map to gst_number in database
    pan: getSettingValue(rawSettings, "pan", "**********"),
    stateCode: getSettingValue(rawSettings, "stateCode", "33"),
    website: getSettingValue(rawSettings, "website", "www.shreejewelry.com"),
    bankDetails: {
      bankName: getSettingValue(rawSettings, "bankName", "State Bank of India"),
      accountNumber: getSettingValue(rawSettings, "accountNumber", "**************"),
      ifscCode: getSettingValue(rawSettings, "ifscCode", "SBIN0001234"),
      branch: getSettingValue(rawSettings, "branch", "T. Nagar Branch"),
    }
  };

  return {
    companySettings,
    isLoading,
    error,
    rawSettings
  };
};

// Utility function to get company settings synchronously (for backward compatibility)
// This will try to get from React Query cache first, then fall back to localStorage
export const getCompanySettingsSync = (): CompanySettings => {
  // Try to get from React Query cache first
  try {
    // This is a fallback - in practice, components should use the hook
    const savedSettings = localStorage.getItem('companySettings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      return {
        name: settings.name || 'Shree Jewelry House',
        address: settings.address || '123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu',
        phone: settings.phone || '+91 98765 43210',
        email: settings.email || '<EMAIL>',
        gstin: settings.gstin || '33AAAAA0000A1Z5',
        pan: settings.pan || '**********',
        stateCode: settings.stateCode || '33',
        website: settings.website || 'www.shreejewelry.com',
        bankDetails: {
          bankName: settings.bankName || 'State Bank of India',
          accountNumber: settings.accountNumber || '**************',
          ifscCode: settings.ifscCode || 'SBIN0001234',
          branch: settings.branch || 'T. Nagar Branch',
        }
      };
    }
  } catch (error) {
    console.error('Error loading company settings:', error);
  }

  // Return default settings if none found
  return {
    name: 'Shree Jewelry House',
    address: '123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu',
    phone: '+91 98765 43210',
    email: '<EMAIL>',
    gstin: '33AAAAA0000A1Z5',
    pan: '**********',
    stateCode: '33',
    website: 'www.shreejewelry.com',
    bankDetails: {
      bankName: 'State Bank of India',
      accountNumber: '**************',
      ifscCode: 'SBIN0001234',
      branch: 'T. Nagar Branch',
    }
  };
};
