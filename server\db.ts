import { createConnection } from 'mysql2/promise';
import { drizzle } from 'drizzle-orm/mysql2';
import * as schema from "@shared/schema";
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

let connection: any = null;
let db: any = null;

async function initializeDatabase() {
  try {
    connection = await createConnection(process.env.DATABASE_URL || 'mysql://localhost:3306/jewelry_tracker');
    db = drizzle(connection, { schema, mode: 'default' });
    console.log('✅ Connected to MySQL database');
  } catch (error) {
    console.warn('⚠️  MySQL connection failed:', error instanceof Error ? error.message : 'Unknown error');
    console.warn('⚠️  Running in demo mode without database');

    // Create a mock database object for demo purposes
    db = {
      select: () => ({ from: () => ({ where: () => [] }) }),
      insert: () => ({ values: () => ({ returning: () => [] }) }),
      update: () => ({ set: () => ({ where: () => [] }) }),
      delete: () => ({ where: () => [] }),
    } as any;
  }
}

// Initialize the database
initializeDatabase();

export { connection, db };