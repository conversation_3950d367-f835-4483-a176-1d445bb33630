import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db.js';
import { templates } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { authenticateToken, requireRole } from '../middleware/auth';
import { storage } from '../storage';

const router = Router();

// Validation schemas
const createTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  category: z.enum(['modern', 'classic', 'minimal', 'luxury', 'custom']),
  colors: z.object({
    primary: z.array(z.number().min(0).max(255)).length(3),
    accent: z.array(z.number().min(0).max(255)).length(3),
    dark: z.array(z.number().min(0).max(255)).length(3),
    lightGray: z.array(z.number().min(0).max(255)).length(3),
    mediumGray: z.array(z.number().min(0).max(255)).length(3)
  }),
  layout: z.object({
    margin: z.number().min(0).max(20),
    headerHeight: z.number().min(20).max(60),
    sectionSpacing: z.number().min(0).max(20),
    fontSize: z.object({
      header: z.number().min(12).max(30),
      subheader: z.number().min(8).max(20),
      body: z.number().min(6).max(16),
      small: z.number().min(4).max(12)
    })
  }),
  settings: z.object({
    showLogo: z.boolean(),
    showWatermark: z.boolean(),
    showGSTBreakdown: z.boolean(),
    showBankDetails: z.boolean(),
    showTermsAndConditions: z.boolean(),
    compactMode: z.boolean(),
    currency: z.string().max(5),
    dateFormat: z.string(),
    numberFormat: z.string()
  }),
  customFields: z.record(z.any()).optional()
});

const updateTemplateSchema = createTemplateSchema.partial();

// Get all templates
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userTemplates = await storage.getTemplates();
    res.json(userTemplates);
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({ error: 'Failed to fetch templates' });
  }
});

// Get template by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const template = await storage.getTemplate(req.params.id);

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    res.json(template);
  } catch (error) {
    console.error('Error fetching template:', error);
    res.status(500).json({ error: 'Failed to fetch template' });
  }
});

// Create new template
router.post('/', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const validatedData = createTemplateSchema.parse(req.body);
    
    const insertResult = await db
      .insert(templates)
      .values({
        ...validatedData,
        isDefault: false,
        isActive: true,
        createdBy: req.user!.id,
        createdAt: new Date(),
        updatedAt: new Date()
      });

    // Fetch the newly created template
    const newTemplate = await db
      .select()
      .from(templates)
      .where(eq(templates.id, validatedData.id))
      .limit(1);

    res.status(201).json(newTemplate[0]);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid template data', details: error.errors });
    }
    console.error('Error creating template:', error);
    res.status(500).json({ error: 'Failed to create template' });
  }
});

// Update template
router.put('/:id', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const validatedData = updateTemplateSchema.parse(req.body);
    
    // Check if template exists and user has permission
    const existingTemplate = await db
      .select()
      .from(templates)
      .where(eq(templates.id, req.params.id))
      .limit(1);

    if (existingTemplate.length === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Only allow updating custom templates or if user is admin
    if (existingTemplate[0].createdBy !== 'system' || req.user!.role === 'admin') {
      const updateResult = await db
        .update(templates)
        .set({
          ...validatedData,
          updatedAt: new Date()
        })
        .where(eq(templates.id, req.params.id));

      // Fetch the updated template
      const updatedTemplate = await db
        .select()
        .from(templates)
        .where(eq(templates.id, req.params.id))
        .limit(1);

      res.json(updatedTemplate[0]);
    } else {
      res.status(403).json({ error: 'Cannot modify system templates' });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid template data', details: error.errors });
    }
    console.error('Error updating template:', error);
    res.status(500).json({ error: 'Failed to update template' });
  }
});

// Delete template
router.delete('/:id', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const existingTemplate = await db
      .select()
      .from(templates)
      .where(eq(templates.id, req.params.id))
      .limit(1);

    if (existingTemplate.length === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Cannot delete system templates
    if (existingTemplate[0].createdBy === 'system') {
      return res.status(403).json({ error: 'Cannot delete system templates' });
    }

    // Soft delete by setting isActive to false
    await db
      .update(templates)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(templates.id, req.params.id));

    res.json({ message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({ error: 'Failed to delete template' });
  }
});

// Set default template
router.post('/:id/set-default', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    // Remove default from all templates
    await db
      .update(templates)
      .set({ isDefault: false, updatedAt: new Date() });

    // Set new default
    const updateResult = await db
      .update(templates)
      .set({ isDefault: true, updatedAt: new Date() })
      .where(and(
        eq(templates.id, req.params.id),
        eq(templates.isActive, true)
      ));

    if (updateResult.affectedRows === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Fetch the updated template
    const updatedTemplate = await db
      .select()
      .from(templates)
      .where(eq(templates.id, req.params.id))
      .limit(1);

    res.json(updatedTemplate[0]);
  } catch (error) {
    console.error('Error setting default template:', error);
    res.status(500).json({ error: 'Failed to set default template' });
  }
});

// Clone template
router.post('/:id/clone', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const { name } = req.body;
    
    if (!name || typeof name !== 'string') {
      return res.status(400).json({ error: 'Template name is required' });
    }

    const originalTemplate = await db
      .select()
      .from(templates)
      .where(and(
        eq(templates.id, req.params.id),
        eq(templates.isActive, true)
      ))
      .limit(1);

    if (originalTemplate.length === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }

    const original = originalTemplate[0];

    // Generate new ID for the cloned template
    const cloneId = `${original.id}-copy-${Date.now()}`;

    const insertResult = await db
      .insert(templates)
      .values({
        id: cloneId,
        name,
        description: `Copy of ${original.description}`,
        category: original.category,
        colors: original.colors,
        layout: original.layout,
        settings: original.settings,
        customFields: original.customFields,
        isDefault: false,
        isActive: true,
        createdBy: req.user!.id,
        createdAt: new Date(),
        updatedAt: new Date()
      });

    // Fetch the newly created template
    const clonedTemplate = await db
      .select()
      .from(templates)
      .where(eq(templates.id, cloneId))
      .limit(1);

    res.status(201).json(clonedTemplate[0]);
  } catch (error) {
    console.error('Error cloning template:', error);
    res.status(500).json({ error: 'Failed to clone template' });
  }
});

// Get template statistics
router.get('/stats/overview', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const stats = await storage.getTemplateStats();
    res.json(stats);
  } catch (error) {
    console.error('Error fetching template stats:', error);
    res.status(500).json({ error: 'Failed to fetch template statistics' });
  }
});

// Seed default templates endpoint (for development/admin use)
router.post('/seed', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    await storage.seedDefaultTemplates();
    res.json({ message: "Default templates seeded successfully" });
  } catch (error) {
    console.error("Error during template seeding:", error);
    res.status(500).json({ message: "Failed to seed default templates" });
  }
});

export default router;