import { db } from "./db";
import { inventoryItems } from "@shared/schema";
import { eq } from "drizzle-orm";

async function updateInventoryValues() {
  console.log("🔄 Updating inventory values...");

  try {
    // Get all inventory items
    const items = await db.select().from(inventoryItems);
    
    for (const item of items) {
      // Calculate realistic values based on item type and weight
      let makingCharges = 0;
      let stoneCharges = 0;
      
      if (item.itemName.toLowerCase().includes('chain')) {
        makingCharges = Math.floor(Math.random() * 5000) + 2000; // 2000-7000
        stoneCharges = Math.floor(Math.random() * 1000) + 500;   // 500-1500
      } else if (item.itemName.toLowerCase().includes('ring')) {
        makingCharges = Math.floor(Math.random() * 3000) + 1500; // 1500-4500
        stoneCharges = Math.floor(Math.random() * 2000) + 1000;  // 1000-3000
      } else if (item.itemName.toLowerCase().includes('necklace')) {
        makingCharges = Math.floor(Math.random() * 8000) + 5000; // 5000-13000
        stoneCharges = Math.floor(Math.random() * 3000) + 2000;  // 2000-5000
      } else if (item.itemName.toLowerCase().includes('earring')) {
        makingCharges = Math.floor(Math.random() * 2000) + 1000; // 1000-3000
        stoneCharges = Math.floor(Math.random() * 1500) + 800;   // 800-2300
      } else {
        // Default values for other items
        makingCharges = Math.floor(Math.random() * 4000) + 2000; // 2000-6000
        stoneCharges = Math.floor(Math.random() * 2000) + 1000;  // 1000-3000
      }

      // Update the item
      await db.update(inventoryItems)
        .set({
          makingCharges: makingCharges.toString(),
          stoneCharges: stoneCharges.toString()
        })
        .where(eq(inventoryItems.id, item.id));

      console.log(`✅ Updated ${item.itemName}: Making ₹${makingCharges}, Stone ₹${stoneCharges}`);
    }

    console.log(`\n🎉 Updated ${items.length} inventory items with realistic values!`);

  } catch (error) {
    console.error("❌ Error updating inventory values:", error);
  }
}

// Run the update
updateInventoryValues()
  .then(() => {
    console.log("\n✅ Inventory value update complete!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Failed to update inventory values:", error);
    process.exit(1);
  });
