const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_NAME || 'jewelry_tracker'
  });

  try {
    console.log('🔄 Running migration: Add template_id to invoices table...');
    
    // Check if column already exists
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'invoices' AND COLUMN_NAME = 'template_id'
    `, [process.env.DB_NAME || 'jewelry_tracker']);
    
    if (columns.length > 0) {
      console.log('✅ Column template_id already exists in invoices table');
      return;
    }
    
    // Add the column
    await connection.execute(`
      ALTER TABLE invoices 
      ADD COLUMN template_id VARCHAR(50) NULL 
      COMMENT 'Reference to the template used for this invoice'
    `);
    
    console.log('✅ Added template_id column to invoices table');
    
    // Add index
    await connection.execute(`
      CREATE INDEX idx_invoices_template_id ON invoices(template_id)
    `);
    
    console.log('✅ Added index for template_id column');
    
    // Update existing invoices with default template
    const [defaultTemplate] = await connection.execute(`
      SELECT id FROM templates WHERE isDefault = true AND isActive = true LIMIT 1
    `);
    
    if (defaultTemplate.length > 0) {
      const templateId = defaultTemplate[0].id;
      await connection.execute(`
        UPDATE invoices SET template_id = ? WHERE template_id IS NULL
      `, [templateId]);
      
      console.log(`✅ Updated existing invoices to use default template: ${templateId}`);
    }
    
    console.log('🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
  } finally {
    await connection.end();
  }
}

runMigration();
