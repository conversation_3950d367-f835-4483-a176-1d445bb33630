import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { testCalculationExample, formatCurrency, formatWeight, formatPercentage } from "@/lib/jewelry-calculations";

export default function CalculationDemo() {
  const result = testCalculationExample();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🧮 Jewelry Calculation Demo
          <Badge variant="secondary">22K Gold Ring</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Input Values */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
          <div className="space-y-2">
            <h4 className="font-semibold text-blue-800">Input Values</h4>
            <div className="text-sm space-y-1">
              <div>Gross Weight: <span className="font-mono">{formatWeight(16.210)}</span></div>
              <div>Stone Weight: <span className="font-mono">{formatWeight(0.345)}</span></div>
              <div>Purity: <span className="font-mono font-bold text-green-600">96% (22K Gold)</span></div>
              <div>Rate/g: <span className="font-mono">{formatCurrency(10112)}</span></div>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold text-blue-800">Additional Charges</h4>
            <div className="text-sm space-y-1">
              <div>Wastage: <span className="font-mono">1%</span></div>
              <div>Making Charges: <span className="font-mono">₹150/g</span></div>
              <div>Stone Amount: <span className="font-mono">{formatCurrency(5600)}</span></div>
            </div>
          </div>
        </div>

        {/* Calculations */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-800">Step-by-Step Calculation</h4>
          
          <div className="grid grid-cols-1 gap-2 text-sm">
            <div className="flex justify-between p-2 bg-gray-50 rounded">
              <span>Net Weight = G.Wt - Stone Wt</span>
              <span className="font-mono">{formatWeight(result.netWeight)}</span>
            </div>
            
            <div className="flex justify-between p-2 bg-gray-50 rounded">
              <span>Fine Weight = Net Wt × 96%</span>
              <span className="font-mono">{formatWeight(result.fineWeight)}</span>
            </div>
            
            <div className="flex justify-between p-2 bg-green-50 rounded">
              <span className="font-semibold">Gold Value = Fine Wt × Rate</span>
              <span className="font-mono font-semibold text-green-600">{formatCurrency(result.metalValue)}</span>
            </div>
            
            <div className="flex justify-between p-2 bg-gray-50 rounded">
              <span>Making Charges = Net Wt × ₹150</span>
              <span className="font-mono">{formatCurrency(result.makingCharges)}</span>
            </div>
            
            <div className="flex justify-between p-2 bg-gray-50 rounded">
              <span>Stone Amount</span>
              <span className="font-mono">{formatCurrency(result.stoneAmount)}</span>
            </div>
          </div>
        </div>

        {/* Final Totals */}
        <div className="space-y-2 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
          <div className="flex justify-between font-semibold">
            <span>Total Before Tax</span>
            <span className="font-mono">{formatCurrency(result.totalBeforeTax)}</span>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span>CGST (1.5%)</span>
              <span className="font-mono">{formatCurrency(result.cgst)}</span>
            </div>
            <div className="flex justify-between">
              <span>SGST (1.5%)</span>
              <span className="font-mono">{formatCurrency(result.sgst)}</span>
            </div>
          </div>
          
          <div className="flex justify-between text-lg font-bold text-green-700 border-t pt-2">
            <span>Grand Total</span>
            <span className="font-mono">{formatCurrency(result.grandTotal)}</span>
          </div>
        </div>

        {/* Purity Reference */}
        <div className="p-4 bg-purple-50 rounded-lg">
          <h4 className="font-semibold text-purple-800 mb-2">Industry Standard Purity Reference</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-semibold mb-1">Gold</div>
              <div>999 (24K) = 99.9%</div>
              <div className="font-bold text-green-600">916 (22K) = 96.0%</div>
              <div>750 (18K) = 75.0%</div>
            </div>
            <div>
              <div className="font-semibold mb-1">Silver</div>
              <div>999 = 99.9%</div>
              <div>925 = 92.5%</div>
              <div>800 = 80.0%</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
