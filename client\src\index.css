@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(154, 50%, 20%);
  --primary-light: hsl(154, 46%, 35%);
  --primary-dark: hsl(154, 100%, 8%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(38, 92%, 50%);
  --accent-light: hsl(45, 93%, 58%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --success: hsl(142, 71%, 45%);
  --warning: hsl(38, 92%, 50%);
  --error: hsl(0, 84%, 60%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Neutral colors */
  --neutral-50: hsl(210, 40%, 98%);
  --neutral-100: hsl(210, 40%, 96%);
  --neutral-200: hsl(214, 32%, 91%);
  --neutral-300: hsl(213, 27%, 84%);
  --neutral-400: hsl(215, 20%, 65%);
  --neutral-500: hsl(215, 16%, 47%);
  --neutral-600: hsl(215, 19%, 35%);
  --neutral-700: hsl(215, 25%, 27%);
  --neutral-800: hsl(217, 33%, 17%);
  --neutral-900: hsl(222, 84%, 5%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(154, 50%, 20%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(38, 92%, 50%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
}

/* Custom component styles */
.jewelry-primary {
  background-color: hsl(154, 50%, 20%);
}

.jewelry-primary-light {
  background-color: hsl(154, 46%, 35%);
}

.jewelry-accent {
  background-color: hsl(38, 92%, 50%);
}

.jewelry-accent-light {
  background-color: hsl(45, 93%, 58%);
}
