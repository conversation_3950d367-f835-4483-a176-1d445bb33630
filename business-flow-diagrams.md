# Jewelry Tracker Business Flow Diagrams

## 1. Complete Business Flow Overview

```mermaid
graph TB
    %% User Management & Authentication
    A[User Login] --> B{Authentication}
    B -->|Success| C[Dashboard]
    B -->|Fail| A
    
    %% Main Dashboard
    C --> D[Metal Rates Management]
    C --> E[Supplier Management]
    C --> F[Metal Procurement]
    C --> G[Inventory Management]
    C --> H[Customer Management]
    C --> I[Billing & Invoicing]
    C --> J[Payment Management]
    C --> K[Reports & Analytics]
    C --> L[Template Management]
    C --> M[Settings]
    
    %% Metal Rates Management
    D --> D1[Auto Rate Updates]
    D --> D2[Manual Rate Entry]
    D --> D3[Rate History]
    D1 --> D4[API Integration]
    D4 --> D5[Gold/Silver/Platinum Rates]
    
    %% Supplier Management
    E --> E1[Add Suppliers]
    E --> E2[Supplier Details]
    E --> E3[Contact Management]
    E1 --> E4[GSTIN Validation]
    E2 --> E5[Credit Terms]
    
    %% Metal Procurement
    F --> F1[Purchase Orders]
    F --> F2[Metal Receipt]
    F --> F3[Quality Check]
    F1 --> F4[Supplier Selection]
    F2 --> F5[Weight Verification]
    F3 --> F6[Purity Testing]
    F6 --> G
    
    %% Inventory Management
    G --> G1[Add Jewelry Items]
    G --> G2[Stock Management]
    G --> G3[Barcode Generation]
    G1 --> G4[Item Master Creation]
    G4 --> G5[Design Details]
    G4 --> G6[Weight Calculations]
    G4 --> G7[Making Charges]
    G4 --> G8[Stone Details]
    G6 --> G9[Gross Weight]
    G6 --> G10[Net Weight]
    G6 --> G11[Wastage %]
    G8 --> G12[Stone Weight]
    G8 --> G13[Stone Charges]
    
    %% Customer Management
    H --> H1[Customer Registration]
    H --> H2[Credit Management]
    H --> H3[Customer Categories]
    H1 --> H4[GSTIN Validation]
    H1 --> H5[Address Details]
    H2 --> H6[Credit Limits]
    H2 --> H7[Outstanding Tracking]
    
    %% Billing & Invoicing
    I --> I1[Create Invoice]
    I --> I2[Template Selection]
    I --> I3[Item Selection]
    I --> I4[Price Calculation]
    I --> I5[GST Calculation]
    I --> I6[PDF Generation]
    
    I1 --> I7[Customer Selection]
    I3 --> I8[Inventory Lookup]
    I4 --> I9[Live Rate Application]
    I4 --> I10[Making Charges]
    I4 --> I11[Value Addition]
    I5 --> I12[CGST/SGST/IGST]
    I6 --> I13[Professional PDF]
    
    %% Template Management
    L --> L1[Template Creation]
    L --> L2[Template Customization]
    L --> L3[Default Template]
    L1 --> L4[Color Schemes]
    L1 --> L5[Layout Settings]
    L1 --> L6[Font Configurations]
    L2 --> L7[Company Branding]
    L3 --> L8[Auto Selection]
    
    %% Payment Management
    J --> J1[Payment Entry]
    J --> J2[Payment Methods]
    J --> J3[Outstanding Management]
    J1 --> J4[Cash/Bank/Card]
    J2 --> J5[Payment Tracking]
    J3 --> J6[Aging Analysis]
    
    %% Reports & Analytics
    K --> K1[Sales Reports]
    K --> K2[Inventory Reports]
    K --> K3[GST Reports]
    K --> K4[Financial Reports]
    K1 --> K5[Daily Sales]
    K1 --> K6[Customer-wise Sales]
    K2 --> K7[Stock Valuation]
    K2 --> K8[Movement Analysis]
    K3 --> K9[GSTR-1 Data]
    K3 --> K10[HSN Summary]
    
    %% Settings
    M --> M1[Company Settings]
    M --> M2[User Management]
    M --> M3[System Configuration]
    M1 --> M4[Company Details]
    M1 --> M5[Bank Details]
    M1 --> M6[GST Configuration]
    
    %% Data Flow Connections
    D5 --> I9
    G --> I8
    H7 --> J3
    I13 --> J1
    I --> K1
    J --> K4
    G --> K2
```

## 2. Daily Operations Sequence Flow

```mermaid
sequenceDiagram
    participant U as User/Staff
    participant S as System
    participant API as Metal Rate API
    participant DB as Database
    participant PDF as PDF Generator
    participant C as Customer
    
    Note over U,C: Daily Operations Flow
    
    %% Morning Setup
    U->>S: Login to System
    S->>DB: Authenticate User
    S->>U: Dashboard Access
    
    %% Metal Rate Updates
    U->>S: Check Metal Rates
    S->>API: Fetch Latest Rates
    API->>S: Gold/Silver/Platinum Rates
    S->>DB: Update Rate History
    S->>U: Display Current Rates
    
    %% Customer Walk-in
    C->>U: Inquiry for Jewelry
    U->>S: Search Inventory
    S->>DB: Query Available Items
    DB->>S: Item Details (Weight, Design, etc.)
    S->>U: Display Items with Live Pricing
    
    %% Price Calculation
    U->>S: Select Items for Quote
    S->>S: Calculate Price
    Note over S: Price = (Net Weight × Live Rate) + Making Charges + Stone Charges + GST
    S->>U: Display Total Price
    
    %% Invoice Creation
    alt Customer Agrees to Purchase
        U->>S: Create Invoice
        S->>U: Template Selection
        U->>S: Select Template & Customer
        S->>DB: Fetch Customer Details
        S->>S: Generate Invoice Data
        S->>PDF: Create Professional PDF
        PDF->>S: Generated Invoice
        S->>DB: Save Invoice Record
        S->>U: Invoice Created Successfully
        
        %% Payment Processing
        C->>U: Make Payment
        U->>S: Record Payment
        S->>DB: Update Payment Records
        S->>DB: Update Customer Outstanding
        S->>U: Payment Recorded
        
        %% Inventory Update
        S->>DB: Reduce Inventory Quantity
        S->>S: Update Stock Levels
        
    else Customer Requests Quote
        U->>S: Generate Quotation
        S->>PDF: Create Quote PDF
        PDF->>U: Quotation Document
    end
    
    %% End of Day Reports
    Note over U,DB: End of Day Process
    U->>S: Generate Daily Reports
    S->>DB: Fetch Sales Data
    S->>DB: Fetch Payment Data
    S->>DB: Fetch Inventory Data
    DB->>S: Consolidated Data
    S->>U: Daily Sales Report
    S->>U: Outstanding Report
    S->>U: Inventory Status
```

## 3. Data Flow Architecture

```mermaid
graph LR
    %% External Data Sources
    API[Metal Rate APIs] --> |Live Rates| RateEngine[Rate Engine]
    Suppliers[Suppliers] --> |Procurement Data| ProcEngine[Procurement Engine]

    %% Core Data Engines
    RateEngine --> |Current Rates| PriceCalc[Price Calculator]
    ProcEngine --> |Metal Stock| Inventory[Inventory Engine]

    %% Master Data
    CustomerDB[(Customer Master)] --> |Customer Info| BillEngine[Billing Engine]
    InventoryDB[(Inventory Master)] --> |Item Details| Inventory
    SupplierDB[(Supplier Master)] --> |Supplier Info| ProcEngine
    TemplateDB[(Template Master)] --> |Design Templates| PDFGen[PDF Generator]

    %% Business Logic Engines
    Inventory --> |Available Stock| BillEngine
    PriceCalc --> |Live Pricing| BillEngine
    BillEngine --> |Invoice Data| PDFGen
    BillEngine --> |Transaction Data| PaymentEngine[Payment Engine]

    %% Output Systems
    PDFGen --> |Professional PDFs| FileSystem[File System]
    PaymentEngine --> |Payment Records| AccountingDB[(Accounting DB)]
    BillEngine --> |Sales Data| ReportEngine[Report Engine]

    %% Reporting & Analytics
    ReportEngine --> |Sales Reports| Dashboard[Dashboard]
    ReportEngine --> |GST Reports| Compliance[GST Compliance]
    ReportEngine --> |Inventory Reports| StockAnalysis[Stock Analysis]

    %% User Interfaces
    Dashboard --> |Real-time Data| WebUI[Web Interface]
    WebUI --> |User Actions| BillEngine
    WebUI --> |Configuration| SettingsEngine[Settings Engine]

    %% Settings & Configuration
    SettingsEngine --> |Company Settings| PDFGen
    SettingsEngine --> |User Preferences| WebUI
    SettingsEngine --> |System Config| RateEngine

    %% Data Storage
    TransactionDB[(Transaction DB)] --> |Historical Data| ReportEngine
    BillEngine --> |Invoice Records| TransactionDB
    PaymentEngine --> |Payment History| TransactionDB
```

## 4. Complete Business Workflow

```mermaid
flowchart TD
    Start([Business Day Starts]) --> Setup[System Setup & Rate Updates]

    %% Setup Phase
    Setup --> RateCheck{Check Metal Rates}
    RateCheck -->|Auto Update| API[Fetch from API]
    RateCheck -->|Manual Entry| Manual[Manual Rate Entry]
    API --> RateUpdate[Update System Rates]
    Manual --> RateUpdate

    %% Procurement Cycle
    RateUpdate --> ProcCheck{Need Metal Procurement?}
    ProcCheck -->|Yes| Supplier[Select Supplier]
    ProcCheck -->|No| CustomerFlow[Customer Operations]

    Supplier --> PO[Create Purchase Order]
    PO --> Receive[Receive Metal]
    Receive --> QualityCheck[Quality & Purity Check]
    QualityCheck --> UpdateStock[Update Metal Stock]
    UpdateStock --> CreateItems[Create Jewelry Items]

    %% Item Creation
    CreateItems --> ItemDetails[Enter Item Details]
    ItemDetails --> Weights[Gross/Net Weight]
    ItemDetails --> Design[Design & Making Charges]
    ItemDetails --> Stones[Stone Details & Charges]
    ItemDetails --> HSN[HSN Code & Barcode]

    Weights --> PriceCalc[Auto Price Calculation]
    Design --> PriceCalc
    Stones --> PriceCalc
    HSN --> PriceCalc
    PriceCalc --> AddInventory[Add to Inventory]
    AddInventory --> CustomerFlow

    %% Customer Operations
    CustomerFlow --> CustomerType{Customer Type}
    CustomerType -->|New| NewCustomer[Register New Customer]
    CustomerType -->|Existing| ExistingCustomer[Select Customer]

    NewCustomer --> CustomerDetails[Enter Customer Details]
    CustomerDetails --> GSTIN[GSTIN Validation]
    GSTIN --> CreditSetup[Setup Credit Terms]
    CreditSetup --> ExistingCustomer

    %% Sales Process
    ExistingCustomer --> ShowItems[Display Available Items]
    ShowItems --> LivePricing[Apply Live Pricing]
    LivePricing --> CustomerChoice{Customer Decision}

    CustomerChoice -->|Purchase| CreateInvoice[Create Invoice]
    CustomerChoice -->|Quote Only| GenerateQuote[Generate Quotation]
    CustomerChoice -->|Not Interested| EndSale[End Sale Process]

    %% Invoice Generation
    CreateInvoice --> SelectTemplate[Select Invoice Template]
    SelectTemplate --> ItemSelection[Select Items & Quantities]
    ItemSelection --> PriceCalculation[Calculate Final Prices]
    PriceCalculation --> GSTCalc[Calculate GST]
    GSTCalc --> InvoicePreview[Invoice Preview]
    InvoicePreview --> GeneratePDF[Generate Professional PDF]
    GeneratePDF --> SaveInvoice[Save Invoice to Database]

    %% Payment Processing
    SaveInvoice --> PaymentType{Payment Method}
    PaymentType -->|Cash| CashPayment[Record Cash Payment]
    PaymentType -->|Bank| BankPayment[Record Bank Payment]
    PaymentType -->|Credit| CreditSale[Record Credit Sale]
    PaymentType -->|Partial| PartialPayment[Record Partial Payment]

    CashPayment --> UpdateInventory[Update Inventory Quantities]
    BankPayment --> UpdateInventory
    CreditSale --> UpdateOutstanding[Update Customer Outstanding]
    PartialPayment --> UpdateOutstanding
    UpdateOutstanding --> UpdateInventory

    %% Quotation Process
    GenerateQuote --> QuotePDF[Generate Quote PDF]
    QuotePDF --> FollowUp[Schedule Follow-up]
    FollowUp --> EndSale

    %% Inventory Management
    UpdateInventory --> StockCheck{Low Stock Alert?}
    StockCheck -->|Yes| ReorderAlert[Generate Reorder Alert]
    StockCheck -->|No| TransactionComplete[Transaction Complete]
    ReorderAlert --> TransactionComplete

    %% End of Day Process
    TransactionComplete --> MoreCustomers{More Customers?}
    MoreCustomers -->|Yes| CustomerFlow
    MoreCustomers -->|No| EODReports[Generate End of Day Reports]

    EODReports --> SalesReport[Daily Sales Report]
    EODReports --> InventoryReport[Inventory Status Report]
    EODReports --> OutstandingReport[Customer Outstanding Report]
    EODReports --> GSTReport[GST Summary Report]

    SalesReport --> DataBackup[Data Backup]
    InventoryReport --> DataBackup
    OutstandingReport --> DataBackup
    GSTReport --> DataBackup

    DataBackup --> End([Business Day Ends])
    EndSale --> MoreCustomers
```

## How to Use These Diagrams

### Method 1: Mermaid Live Editor
1. Visit: https://mermaid.live
2. Copy any diagram code above
3. Paste into the editor
4. Download as PNG, SVG, or PDF

### Method 2: VS Code Extension
1. Install "Mermaid Preview" extension
2. Create a .md file with the diagram code
3. Use preview to view and export

### Method 3: Online Tools
- **Draw.io**: Import Mermaid code
- **Lucidchart**: Mermaid integration
- **Creately**: Mermaid support

### Method 4: Command Line
```bash
npm install -g @mermaid-js/mermaid-cli
mmdc -i diagram.md -o diagram.png
```
