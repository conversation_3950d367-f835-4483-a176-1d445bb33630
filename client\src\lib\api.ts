import { apiRequest } from "./queryClient";

// Re-export apiRequest for use in other modules
export { apiRequest };

export interface DashboardStats {
  todaysSales: string;
  inventoryValue: string;
  inventoryCount: number;
  pendingPayments: string;
  overdueCount: number;
  lowStockCount: number;
}

export async function fetchDashboardStats(): Promise<DashboardStats> {
  const response = await apiRequest("GET", "/api/dashboard/stats");
  return response.json();
}

export async function fetchCustomers() {
  const response = await apiRequest("GET", "/api/customers");
  return response.json();
}

export async function fetchInventoryItems() {
  const response = await apiRequest("GET", "/api/inventory");
  return response.json();
}

export async function fetchNextItemCode(metalType: string, prefix?: string) {
  const params = new URLSearchParams({ metalType });
  if (prefix) params.append('prefix', prefix);
  const response = await apiRequest("GET", `/api/inventory/next-code?${params}`);
  return response.json();
}

export async function fetchMetalRates() {
  const response = await apiRequest("GET", "/api/metal-rates/current");
  return response.json();
}

export async function fetchInvoices() {
  const response = await apiRequest("GET", "/api/invoices");
  return response.json();
}

export async function fetchInvoice(id: number) {
  const response = await apiRequest("GET", `/api/invoices/${id}`);
  return response.json();
}

export async function fetchInvoicePDF(id: number) {
  const response = await apiRequest("GET", `/api/invoices/${id}/pdf`);
  return response.json();
}

export async function createInvoice(invoice: any) {
  const response = await apiRequest("POST", "/api/invoices", invoice);
  return response.json();
}

export async function createCustomer(customer: any) {
  const response = await apiRequest("POST", "/api/customers", customer);
  return response.json();
}

export async function createInventoryItem(item: any) {
  try {
    const response = await apiRequest("POST", "/api/inventory", item);
    const result = await response.json();

    // Handle the new response format
    if (result.success) {
      return result.data; // Return the actual inventory item data
    } else {
      // Handle error response
      throw new Error(result.message || result.error || "Failed to create inventory item");
    }
  } catch (error) {
    console.error("Error creating inventory item:", error);
    throw error;
  }
}

export async function updateInventoryItem(id: number, item: any) {
  try {
    const response = await apiRequest("PUT", `/api/inventory/${id}`, item);
    const result = await response.json();

    // Handle the new response format
    if (result.success) {
      return result.data; // Return the updated inventory item data
    } else {
      // Handle error response
      throw new Error(result.message || result.error || "Failed to update inventory item");
    }
  } catch (error) {
    console.error("Error updating inventory item:", error);
    throw error;
  }
}

export async function deleteInventoryItem(id: number) {
  try {
    const response = await apiRequest("DELETE", `/api/inventory/${id}`);
    const result = await response.json();

    // Handle the new response format
    if (result.success) {
      return result.data; // Return the deletion confirmation
    } else {
      // Handle error response
      throw new Error(result.message || result.error || "Failed to delete inventory item");
    }
  } catch (error) {
    console.error("Error deleting inventory item:", error);
    throw error;
  }
}

export async function createPayment(payment: any) {
  const response = await apiRequest("POST", "/api/payments", payment);
  return response.json();
}

// UPDATE operations
export async function updateCustomer(id: number, customer: any) {
  const response = await apiRequest("PUT", `/api/customers/${id}`, customer);
  return response.json();
}



export async function updateInvoice(id: number, invoice: any) {
  const response = await apiRequest("PUT", `/api/invoices/${id}`, invoice);
  return response.json();
}

export async function updatePayment(id: number, payment: any) {
  const response = await apiRequest("PUT", `/api/payments/${id}`, payment);
  return response.json();
}

export async function updateSupplier(id: number, supplier: any) {
  const response = await apiRequest("PUT", `/api/suppliers/${id}`, supplier);
  return response.json();
}

export async function updateMetalProcurement(id: number, procurement: any) {
  const response = await apiRequest("PUT", `/api/metal-procurement/${id}`, procurement);
  return response.json();
}

export async function updateMetalRate(id: number, rate: any) {
  const response = await apiRequest("PUT", `/api/metal-rates/${id}`, rate);
  return response.json();
}

// DELETE operations
export async function deleteCustomer(id: number) {
  const response = await apiRequest("DELETE", `/api/customers/${id}`);
  return response.ok;
}



export async function deleteInvoice(id: number) {
  const response = await apiRequest("DELETE", `/api/invoices/${id}`);
  return response.ok;
}

export async function deletePayment(id: number) {
  const response = await apiRequest("DELETE", `/api/payments/${id}`);
  return response.ok;
}

export async function deleteSupplier(id: number) {
  const response = await apiRequest("DELETE", `/api/suppliers/${id}`);
  return response.ok;
}

export async function deleteMetalProcurement(id: number) {
  const response = await apiRequest("DELETE", `/api/metal-procurement/${id}`);
  return response.ok;
}

export async function deleteMetalRate(id: number) {
  const response = await apiRequest("DELETE", `/api/metal-rates/${id}`);
  return response.ok;
}

// Additional fetch operations
export async function fetchSuppliers() {
  const response = await apiRequest("GET", "/api/suppliers");
  return response.json();
}

export async function fetchMetalProcurements() {
  const response = await apiRequest("GET", "/api/metal-procurement");
  return response.json();
}

export async function fetchPayments() {
  const response = await apiRequest("GET", "/api/payments");
  return response.json();
}

export async function createSupplier(supplier: any) {
  const response = await apiRequest("POST", "/api/suppliers", supplier);
  return response.json();
}

export async function createMetalProcurement(procurement: any) {
  const response = await apiRequest("POST", "/api/metal-procurement", procurement);
  return response.json();
}

export async function createMetalRate(rate: any) {
  console.log("Creating metal rate with data:", rate);
  try {
    const response = await apiRequest("POST", "/api/metal-rates", rate);
    console.log("API response status:", response.status);
    const result = await response.json();
    console.log("API response data:", result);
    return result;
  } catch (error) {
    console.error("Error in createMetalRate:", error);
    throw error;
  }
}
