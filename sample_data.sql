-- Insert sample metal rates
INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, is_active) VALUES
('gold', '999', 6845.00, CURDATE(), 1),
('gold', '916', 6273.00, CURDATE(), 1),
('silver', '925', 84.50, CURDATE(), 1);

-- Insert sample suppliers
INSERT INTO suppliers (name, contact_person, phone, email, address, gstin, supplier_type) VALUES
('Chennai Gold Imports', '<PERSON><PERSON>', '+91-9876543210', 'raj<PERSON>@chennaigold.com', 'T. Nagar, Chennai', '33ABCDE1234F5Z6', 'bullion_dealer'),
('Tamil Nadu Refiners', '<PERSON><PERSON>', '+91-9876543211', '<EMAIL>', 'Coimbatore', '33FGHIJ5678K9L0', 'refiner');

-- Insert sample customers
INSERT INTO customers (name, contact_person, phone, email, address, gstin, pan, state_code, credit_limit, outstanding_amount, discount_type, price_type) VALUES
('Kumar Jewellers', '<PERSON><PERSON>', '+91-9876543220', 'suresh@kumar<PERSON>wels.com', 'Anna Nagar, Chennai', '33QWERT1234U5I6', '**********', '33', 500000.00, 125000.00, 'percentage', 'standard'),
('Lakshmi Ornaments', 'Lakshmi Devi', '+91-9876543221', '<EMAIL>', 'Madurai', '33YUIOP5678H9J0', '**********', '33', 300000.00, 75000.00, 'fixed', 'special'),
('Golden Palace', 'Ravi Chandran', '+91-9876543222', '<EMAIL>', 'Salem', '33ASDFG9012Q3W4', '**********', '33', 750000.00, 0.00, 'none', 'standard');

-- Insert sample inventory items
INSERT INTO inventory_items (item_code, item_name, design_name, metal_type, purity, gross_weight, net_weight, stone_weight, wastage_percentage, making_charges, stone_charges, value_addition_percentage, hsn_code, barcode, quantity) VALUES
('GC001', 'Gold Chain 22K', 'Traditional Chain', 'gold', '916', 25.500, 24.000, 0.000, 6.00, 2400.00, 0.00, 15.00, '71131900', 'BC001', 12),
('GB002', 'Gold Bangles Set', 'Floral Design', 'gold', '916', 45.750, 42.500, 1.250, 7.50, 4250.00, 5000.00, 20.00, '71131900', 'BC002', 8),
('SR003', 'Silver Ring', 'Contemporary', 'silver', '925', 8.200, 7.800, 0.400, 5.00, 780.00, 1200.00, 10.00, '71131900', 'BC003', 25),
('GE004', 'Gold Earrings', 'Antique Style', 'gold', '916', 12.300, 11.500, 0.800, 6.50, 1150.00, 3500.00, 18.00, '71131900', 'BC004', 6),
('SN005', 'Silver Necklace', 'Modern Design', 'silver', '925', 35.600, 33.200, 2.400, 7.00, 3320.00, 8000.00, 12.00, '71131900', 'BC005', 4);

-- Insert sample invoices
INSERT INTO invoices (invoice_number, customer_id, invoice_date, due_date, subtotal, cgst_amount, sgst_amount, discount_amount, total_amount, paid_amount, balance_amount, status, payment_terms, notes) VALUES
('INV-2024-1001', 1, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 25 DAY), 156750.00, 2351.25, 2351.25, 0.00, 161452.50, 161452.50, 0.00, 'paid', 'net30', 'Bulk order - 5% discount applied'),
('INV-2024-1002', 2, DATE_SUB(CURDATE(), INTERVAL 3 DAY), DATE_ADD(CURDATE(), INTERVAL 27 DAY), 287500.00, 4312.50, 4312.50, 14375.00, 281750.00, 150000.00, 131750.00, 'pending', 'net30', 'Special customer pricing'),
('INV-2024-1003', 3, DATE_SUB(CURDATE(), INTERVAL 1 DAY), DATE_ADD(CURDATE(), INTERVAL 29 DAY), 95420.00, 1431.30, 1431.30, 0.00, 98282.60, 0.00, 98282.60, 'pending', 'net30', '');

-- Insert sample payments
INSERT INTO payments (customer_id, invoice_id, payment_date, amount, payment_mode, reference_number, notes) VALUES
(1, 1, DATE_SUB(CURDATE(), INTERVAL 3 DAY), 161452.50, 'rtgs', 'RTGS240120001', 'Full payment received'),
(2, 2, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 150000.00, 'bank', 'CHQ240120002', 'Partial payment - cheque');

-- Insert sample metal procurement
INSERT INTO metal_procurement (supplier_id, metal_type, purity, weight, purchase_rate, total_amount, invoice_number, invoice_date, hsn_code, gst_amount) VALUES
(1, 'gold', '999', 500.000, 6800.00, 3400000.00, 'SUP-001-2024', DATE_SUB(CURDATE(), INTERVAL 7 DAY), '********', 51000.00),
(2, 'silver', '925', 2000.000, 82.50, 165000.00, 'SUP-002-2024', DATE_SUB(CURDATE(), INTERVAL 5 DAY), '********', 2475.00);
