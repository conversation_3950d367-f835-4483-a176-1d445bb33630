<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generator Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Jewelry Invoice PDF Generator Test</h1>
    
    <div class="test-section">
        <h2>Test Sample Invoice Data</h2>
        <p>This page tests the professional GST-compliant jewelry invoice PDF generators.</p>
        
        <button class="button" onclick="testGSTInvoice()">Generate GST Compliant Invoice</button>
        <button class="button" onclick="testProfessionalInvoice()">Generate Professional Invoice</button>
        <button class="button" onclick="testSimpleInvoice()">Generate Simple Invoice</button>
    </div>

    <div class="test-section">
        <h3>Sample Data Preview</h3>
        <pre id="sampleData"></pre>
    </div>

    <script>
        // Sample invoice data for testing
        const sampleInvoiceData = {
            invoice: {
                id: 1,
                invoiceNumber: "INV-2024-001",
                invoiceDate: "2024-01-15",
                dueDate: "2024-02-15",
                subtotal: "100000.00",
                cgstAmount: "1500.00",
                sgstAmount: "1500.00",
                igstAmount: "0.00",
                discountAmount: "0.00",
                totalAmount: "103000.00",
                paidAmount: "0.00",
                balanceAmount: "103000.00",
                notes: "Thank you for your business!",
                status: "pending",
                ewayBillNumber: "123456789012",
                paymentTerms: "Net 30 days"
            },
            customer: {
                id: 1,
                name: "Lakshmi Ornaments",
                email: "<EMAIL>",
                phone: "+91 98765 43210",
                address: "123, Jewelry Street, T. Nagar, Chennai - 600017, Tamil Nadu",
                gstin: "33BBBBB1234B1Z5",
                pan: "**********",
                stateCode: "33"
            },
            items: [
                {
                    id: 1,
                    itemId: 101,
                    itemName: "Gold Necklace Set",
                    quantity: 1,
                    grossWeight: "25.500",
                    netWeight: "24.200",
                    fineWeight: "22.167",
                    stoneWeight: "1.300",
                    stoneAmount: "5000.00",
                    purityPercentage: "91.6",
                    wastagePercentage: "10.0",
                    ratePerGram: "7800.00",
                    goldValue: "172902.60",
                    makingCharges: "12100.00",
                    stoneCharges: "5000.00",
                    itemTotal: "190002.60",
                    taxableAmount: "184468.16",
                    cgstAmount: "2767.02",
                    sgstAmount: "2767.02",
                    totalAmount: "190002.20",
                    metalType: "Gold",
                    purity: "22K",
                    hsnCode: "7113"
                },
                {
                    id: 2,
                    itemId: 102,
                    itemName: "Gold Bangles Pair",
                    quantity: 2,
                    grossWeight: "15.200",
                    netWeight: "14.800",
                    fineWeight: "13.557",
                    stoneWeight: "0.400",
                    stoneAmount: "2000.00",
                    purityPercentage: "91.6",
                    wastagePercentage: "8.0",
                    ratePerGram: "7800.00",
                    goldValue: "105744.60",
                    makingCharges: "7400.00",
                    stoneCharges: "2000.00",
                    itemTotal: "115144.60",
                    taxableAmount: "111801.55",
                    cgstAmount: "1677.02",
                    sgstAmount: "1677.02",
                    totalAmount: "115155.59",
                    metalType: "Gold",
                    purity: "22K",
                    hsnCode: "7113"
                }
            ],
            company: {
                name: "Shree Jewelry House",
                address: "456, Gold Street, Jewelry Market, Chennai - 600001, Tamil Nadu",
                phone: "+91 98765 43210",
                email: "<EMAIL>",
                gstin: "33AAAAA0000A1Z5",
                pan: "**********",
                stateCode: "33",
                website: "www.shreejewelry.com",
                bankDetails: {
                    bankName: "State Bank of India",
                    accountNumber: "**************",
                    ifscCode: "SBIN0001234",
                    branch: "T. Nagar Branch"
                }
            },
            metalRates: {
                gold: 7800,
                silver: 950,
                date: "15/01/2024"
            }
        };

        // Display sample data
        document.getElementById('sampleData').textContent = JSON.stringify(sampleInvoiceData, null, 2);

        // Simple PDF generator for testing
        function testSimpleInvoice() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Header
            doc.setFontSize(20);
            doc.text('JEWELRY INVOICE', 105, 20, { align: 'center' });
            
            // Company details
            doc.setFontSize(12);
            doc.text(sampleInvoiceData.company.name, 20, 40);
            doc.text(sampleInvoiceData.company.address, 20, 50);
            doc.text(`GSTIN: ${sampleInvoiceData.company.gstin}`, 20, 60);
            
            // Invoice details
            doc.text(`Invoice No: ${sampleInvoiceData.invoice.invoiceNumber}`, 20, 80);
            doc.text(`Date: ${sampleInvoiceData.invoice.invoiceDate}`, 20, 90);
            
            // Customer details
            doc.text('Bill To:', 20, 110);
            doc.text(sampleInvoiceData.customer.name, 20, 120);
            doc.text(sampleInvoiceData.customer.address, 20, 130);
            
            // Items table
            let yPos = 160;
            doc.text('Items:', 20, 150);
            sampleInvoiceData.items.forEach((item, index) => {
                doc.text(`${index + 1}. ${item.itemName}`, 20, yPos);
                doc.text(`Qty: ${item.quantity}`, 30, yPos + 10);
                doc.text(`Weight: ${item.netWeight}g`, 80, yPos + 10);
                doc.text(`Amount: ₹${item.totalAmount}`, 130, yPos + 10);
                yPos += 25;
            });
            
            // Total
            doc.text(`Total Amount: ₹${sampleInvoiceData.invoice.totalAmount}`, 20, yPos + 20);
            
            doc.save('simple-jewelry-invoice.pdf');
        }

        function testGSTInvoice() {
            alert('GST Invoice generator would be loaded here. This requires the full TypeScript modules.');
        }

        function testProfessionalInvoice() {
            alert('Professional Invoice generator would be loaded here. This requires the full TypeScript modules.');
        }
    </script>
</body>
</html>