CREATE TABLE `customers` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`contact_person` varchar(255),
	`phone` varchar(20),
	`email` varchar(255),
	`address` varchar(500),
	`gstin` varchar(15),
	`pan` varchar(10),
	`state_code` varchar(5),
	`credit_limit` decimal(12,2) DEFAULT '0',
	`outstanding_amount` decimal(12,2) DEFAULT '0',
	`discount_type` varchar(20) DEFAULT 'none',
	`discount_value` decimal(5,2) DEFAULT '0',
	`price_type` varchar(20) DEFAULT 'standard',
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `customers_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `inventory_items` (
	`id` int AUTO_INCREMENT NOT NULL,
	`item_code` varchar(100) NOT NULL,
	`item_name` varchar(255) NOT NULL,
	`design_name` varchar(255),
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`gross_weight` decimal(10,3) NOT NULL,
	`net_weight` decimal(10,3) NOT NULL,
	`stone_weight` decimal(10,3) DEFAULT '0',
	`wastage_percentage` decimal(5,2) DEFAULT '0',
	`making_charges` decimal(10,2) DEFAULT '0',
	`stone_charges` decimal(10,2) DEFAULT '0',
	`value_addition_percentage` decimal(5,2) DEFAULT '0',
	`hsn_code` varchar(20) NOT NULL,
	`barcode` varchar(100),
	`quantity` int DEFAULT 1,
	`is_active` boolean DEFAULT true,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `inventory_items_id` PRIMARY KEY(`id`),
	CONSTRAINT `inventory_items_item_code_unique` UNIQUE(`item_code`)
);
--> statement-breakpoint
CREATE TABLE `invoice_items` (
	`id` int AUTO_INCREMENT NOT NULL,
	`invoice_id` int NOT NULL,
	`item_id` int NOT NULL,
	`quantity` int NOT NULL DEFAULT 1,
	`gross_weight` decimal(10,3) NOT NULL,
	`net_weight` decimal(10,3) NOT NULL,
	`rate_per_gram` decimal(10,2) NOT NULL,
	`making_charges` decimal(10,2) DEFAULT '0',
	`stone_charges` decimal(10,2) DEFAULT '0',
	`total_amount` decimal(12,2) NOT NULL,
	CONSTRAINT `invoice_items_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `invoices` (
	`id` int AUTO_INCREMENT NOT NULL,
	`invoice_number` varchar(100) NOT NULL,
	`customer_id` int NOT NULL,
	`invoice_date` date NOT NULL,
	`due_date` date,
	`subtotal` decimal(12,2) NOT NULL,
	`cgst_amount` decimal(10,2) DEFAULT '0',
	`sgst_amount` decimal(10,2) DEFAULT '0',
	`igst_amount` decimal(10,2) DEFAULT '0',
	`discount_amount` decimal(10,2) DEFAULT '0',
	`total_amount` decimal(12,2) NOT NULL,
	`paid_amount` decimal(12,2) DEFAULT '0',
	`balance_amount` decimal(12,2) NOT NULL,
	`status` varchar(20) NOT NULL DEFAULT 'pending',
	`payment_terms` varchar(500),
	`notes` varchar(1000),
	`eway_bill_number` varchar(50),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `invoices_id` PRIMARY KEY(`id`),
	CONSTRAINT `invoices_invoice_number_unique` UNIQUE(`invoice_number`)
);
--> statement-breakpoint
CREATE TABLE `metal_procurement` (
	`id` int AUTO_INCREMENT NOT NULL,
	`supplier_id` int NOT NULL,
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`weight` decimal(10,3) NOT NULL,
	`purchase_rate` decimal(10,2) NOT NULL,
	`total_amount` decimal(12,2) NOT NULL,
	`invoice_number` varchar(100) NOT NULL,
	`invoice_date` date NOT NULL,
	`hsn_code` varchar(20) NOT NULL,
	`gst_amount` decimal(10,2),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `metal_procurement_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `metal_rates` (
	`id` int AUTO_INCREMENT NOT NULL,
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`rate_per_gram` decimal(10,2) NOT NULL,
	`rate_date` date NOT NULL,
	`is_active` boolean DEFAULT true,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `metal_rates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payments` (
	`id` int AUTO_INCREMENT NOT NULL,
	`customer_id` int NOT NULL,
	`invoice_id` int,
	`payment_date` date NOT NULL,
	`amount` decimal(12,2) NOT NULL,
	`payment_mode` varchar(50) NOT NULL,
	`reference_number` varchar(100),
	`notes` varchar(500),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `payments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `suppliers` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`contact_person` varchar(255),
	`phone` varchar(20),
	`email` varchar(255),
	`address` varchar(500),
	`gstin` varchar(15),
	`supplier_type` varchar(50) NOT NULL,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `suppliers_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` int AUTO_INCREMENT NOT NULL,
	`username` varchar(255) NOT NULL,
	`password` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`role` varchar(50) NOT NULL DEFAULT 'user',
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`)
);
--> statement-breakpoint
ALTER TABLE `invoice_items` ADD CONSTRAINT `invoice_items_invoice_id_invoices_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD CONSTRAINT `invoice_items_item_id_inventory_items_id_fk` FOREIGN KEY (`item_id`) REFERENCES `inventory_items`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `invoices` ADD CONSTRAINT `invoices_customer_id_customers_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `metal_procurement` ADD CONSTRAINT `metal_procurement_supplier_id_suppliers_id_fk` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payments` ADD CONSTRAINT `payments_customer_id_customers_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payments` ADD CONSTRAINT `payments_invoice_id_invoices_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE no action ON UPDATE no action;