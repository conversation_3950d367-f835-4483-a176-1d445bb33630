import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Search, Eye, Download, Package } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

async function fetchMetalProcurements() {
  const response = await apiRequest("GET", "/api/metal-procurement");
  return response.json();
}

async function fetchSuppliers() {
  const response = await apiRequest("GET", "/api/suppliers");
  return response.json();
}

async function createMetalProcurement(data: any) {
  const response = await apiRequest("POST", "/api/metal-procurement", data);
  return response.json();
}

async function createSupplier(data: any) {
  const response = await apiRequest("POST", "/api/suppliers", data);
  return response.json();
}

export default function MetalProcurement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isProcurementDialogOpen, setIsProcurementDialogOpen] = useState(false);
  const [isSupplierDialogOpen, setIsSupplierDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: procurements, isLoading } = useQuery({
    queryKey: ["/api/metal-procurement"],
    queryFn: fetchMetalProcurements,
  });

  const { data: suppliers } = useQuery({
    queryKey: ["/api/suppliers"],
    queryFn: fetchSuppliers,
  });

  const createProcurementMutation = useMutation({
    mutationFn: createMetalProcurement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metal-procurement"] });
      setIsProcurementDialogOpen(false);
      toast({
        title: "Success",
        description: "Metal procurement record created successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create metal procurement record",
        variant: "destructive",
      });
    },
  });

  const createSupplierMutation = useMutation({
    mutationFn: createSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/suppliers"] });
      setIsSupplierDialogOpen(false);
      toast({
        title: "Success",
        description: "Supplier created successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create supplier",
        variant: "destructive",
      });
    },
  });

  const filteredProcurements = procurements?.filter((procurement: any) =>
    procurement.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    procurement.metalType.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleProcurementSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = {
      supplierId: parseInt(formData.get("supplierId") as string),
      metalType: formData.get("metalType"),
      purity: formData.get("purity"),
      weight: formData.get("weight"),
      purchaseRate: formData.get("purchaseRate"),
      totalAmount: parseFloat(formData.get("weight") as string) * parseFloat(formData.get("purchaseRate") as string),
      invoiceNumber: formData.get("invoiceNumber"),
      invoiceDate: formData.get("invoiceDate"),
      hsnCode: formData.get("hsnCode"),
      gstAmount: formData.get("gstAmount") || "0",
    };
    createProcurementMutation.mutate(data);
  };

  const handleSupplierSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = {
      name: formData.get("name"),
      contactPerson: formData.get("contactPerson"),
      phone: formData.get("phone"),
      email: formData.get("email"),
      address: formData.get("address"),
      gstin: formData.get("gstin"),
      supplierType: formData.get("supplierType"),
    };
    createSupplierMutation.mutate(data);
  };

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Metal Procurement" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search procurement records..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Dialog open={isSupplierDialogOpen} onOpenChange={setIsSupplierDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="bg-neutral-600 text-white hover:bg-neutral-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Supplier
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Supplier</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSupplierSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Supplier Name</Label>
                        <Input id="name" name="name" required />
                      </div>
                      <div>
                        <Label htmlFor="contactPerson">Contact Person</Label>
                        <Input id="contactPerson" name="contactPerson" />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input id="phone" name="phone" type="tel" />
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" name="email" type="email" />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="address">Address</Label>
                      <Input id="address" name="address" />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="gstin">GSTIN</Label>
                        <Input id="gstin" name="gstin" />
                      </div>
                      <div>
                        <Label htmlFor="supplierType">Supplier Type</Label>
                        <Select name="supplierType" required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bullion_dealer">Bullion Dealer</SelectItem>
                            <SelectItem value="refiner">Refiner</SelectItem>
                            <SelectItem value="bank">Bank</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setIsSupplierDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={createSupplierMutation.isPending}>
                        {createSupplierMutation.isPending ? "Creating..." : "Create Supplier"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>

              <Dialog open={isProcurementDialogOpen} onOpenChange={setIsProcurementDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Procurement
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add Metal Procurement Record</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleProcurementSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="supplierId">Supplier</Label>
                        <Select name="supplierId" required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select supplier" />
                          </SelectTrigger>
                          <SelectContent>
                            {suppliers?.map((supplier: any) => (
                              <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                {supplier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="metalType">Metal Type</Label>
                        <Select name="metalType" required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gold">Gold</SelectItem>
                            <SelectItem value="silver">Silver</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="purity">Purity</Label>
                        <Select name="purity" required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select purity" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="999">999 (24K)</SelectItem>
                            <SelectItem value="995">995</SelectItem>
                            <SelectItem value="916">916 (22K)</SelectItem>
                            <SelectItem value="925">925 (Silver)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="weight">Weight (grams)</Label>
                        <Input id="weight" name="weight" type="number" step="0.001" required />
                      </div>
                      <div>
                        <Label htmlFor="purchaseRate">Rate per Gram (₹)</Label>
                        <Input id="purchaseRate" name="purchaseRate" type="number" step="0.01" required />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="invoiceNumber">Invoice Number</Label>
                        <Input id="invoiceNumber" name="invoiceNumber" required />
                      </div>
                      <div>
                        <Label htmlFor="invoiceDate">Invoice Date</Label>
                        <Input id="invoiceDate" name="invoiceDate" type="date" required />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="hsnCode">HSN Code</Label>
                        <Input id="hsnCode" name="hsnCode" required />
                      </div>
                      <div>
                        <Label htmlFor="gstAmount">GST Amount (₹)</Label>
                        <Input id="gstAmount" name="gstAmount" type="number" step="0.01" />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setIsProcurementDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={createProcurementMutation.isPending}>
                        {createProcurementMutation.isPending ? "Creating..." : "Create Record"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Procurement Records */}
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-neutral-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-neutral-200 rounded w-1/2 mb-4"></div>
                      <div className="h-3 bg-neutral-200 rounded w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredProcurements.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Package className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-neutral-700 mb-2">No procurement records found</h3>
                <p className="text-neutral-500 mb-4">
                  {searchTerm ? "No records match your search criteria." : "Start by adding your first metal procurement record."}
                </p>
                <Button 
                  onClick={() => setIsProcurementDialogOpen(true)}
                  className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Procurement
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredProcurements.map((procurement: any) => (
                <Card key={procurement.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="text-lg font-semibold text-neutral-900">
                            {procurement.invoiceNumber}
                          </h3>
                          <Badge className="bg-[hsl(154,50%,20%)] text-white">
                            {procurement.metalType} ({procurement.purity})
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <p className="text-neutral-600">Supplier ID</p>
                            <p className="font-medium">#{procurement.supplierId}</p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Weight</p>
                            <p className="font-medium">{procurement.weight}g</p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Rate per Gram</p>
                            <p className="font-medium">₹{parseFloat(procurement.purchaseRate).toLocaleString("en-IN")}</p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Total Amount</p>
                            <p className="font-semibold text-lg">
                              ₹{parseInt(procurement.totalAmount).toLocaleString("en-IN")}
                            </p>
                          </div>
                          <div>
                            <p className="text-neutral-600">Invoice Date</p>
                            <p className="font-medium">
                              {new Date(procurement.invoiceDate).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-3 flex items-center space-x-4 text-sm text-neutral-600">
                          <span>HSN: {procurement.hsnCode}</span>
                          {procurement.gstAmount && parseFloat(procurement.gstAmount) > 0 && (
                            <span>GST: ₹{parseInt(procurement.gstAmount).toLocaleString("en-IN")}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  );
}