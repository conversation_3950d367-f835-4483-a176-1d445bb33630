import { Calendar, Bell, ChevronDown, LogOut, User } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { fetchMetalRates } from "@/lib/api";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function Header({ title }: { title: string }) {
  const { user, logout } = useAuth();
  const { data: metalRates } = useQuery({
    queryKey: ["/api/metal-rates/current"],
    queryFn: fetchMetalRates,
  });

  const currentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const goldRate24K = metalRates?.find((rate: any) => rate.metalType === "gold" && rate.purity === "999")?.ratePerGram || "6,845";
  const goldRate22K = metalRates?.find((rate: any) => rate.metalType === "gold" && rate.purity === "916")?.ratePerGram || "6,273";
  const silverRate = metalRates?.find((rate: any) => rate.metalType === "silver" && rate.purity === "925")?.ratePerGram || "84.5";

  return (
    <header className="bg-white shadow-sm border-b border-neutral-200 px-6 py-4 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <h2 className="text-xl font-semibold text-neutral-800">{title}</h2>
        <div className="flex items-center space-x-2 text-sm text-neutral-500">
          <Calendar className="w-4 h-4" />
          <span>{currentDate}</span>
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        {/* Gold Rate Display */}
        <div className="flex items-center space-x-4 bg-[hsl(38,92%,50%)] bg-opacity-10 px-4 py-2 rounded-lg">
          <div className="text-center">
            <p className="text-xs text-neutral-600">Gold 24K</p>
            <p className="font-semibold text-[hsl(38,92%,50%)]">₹{goldRate24K}/g</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-neutral-600">Gold 22K</p>
            <p className="font-semibold text-[hsl(38,92%,50%)]">₹{goldRate22K}/g</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-neutral-600">Silver</p>
            <p className="font-semibold text-neutral-600">₹{silverRate}/g</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="relative p-2 text-neutral-600 hover:text-neutral-800 transition-colors">
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
          </button>
          
          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 text-sm text-neutral-600 hover:text-neutral-800">
                <div className="w-8 h-8 bg-[hsl(154,50%,20%)] rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-neutral-700">{user?.name || "User"}</span>
                <ChevronDown className="w-4 h-4 text-neutral-500" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">{user?.name}</p>
                  <p className="text-xs text-neutral-500">@{user?.username}</p>
                  <p className="text-xs text-neutral-500 capitalize">{user?.role}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} className="text-red-600 focus:text-red-600">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
