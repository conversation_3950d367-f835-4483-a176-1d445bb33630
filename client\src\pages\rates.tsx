import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, TrendingUp, TrendingDown, RefreshCw, History, BarChart3, Settings, Zap, Edit3, Globe, User } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

async function fetchMetalRates() {
  const response = await apiRequest("GET", "/api/metal-rates");
  return response.json();
}

async function fetchCurrentMetalRates() {
  const response = await apiRequest("GET", "/api/metal-rates/current");
  return response.json();
}

async function createMetalRate(data: any) {
  const response = await apiRequest("POST", "/api/metal-rates", data);
  return response.json();
}

// API Integration Functions
async function fetchAPIRates(provider: string, apiKey: string, customUrl?: string, mapping?: any) {
  console.log(`Fetching rates from ${provider} API...`);

  // Simulate API calls for different providers
  switch (provider) {
    case "goldapi":
      return await fetchGoldAPIRates(apiKey);
    case "metalpriceapi":
      return await fetchMetalPriceAPIRates(apiKey);
    case "custom":
      return await fetchCustomAPIRates(apiKey, customUrl, mapping);
    default:
      throw new Error("Unsupported API provider");
  }
}

async function fetchGoldAPIRates(apiKey: string) {
  // Simulate Gold API integration
  // In production, this would call: https://www.goldapi.io/api/XAU/USD
  return {
    gold: {
      "999": { rate: 7850.50, timestamp: new Date().toISOString() },
      "916": { rate: 7536.48, timestamp: new Date().toISOString() },
      "750": { rate: 5887.88, timestamp: new Date().toISOString() }
    },
    silver: {
      "999": { rate: 95.25, timestamp: new Date().toISOString() },
      "925": { rate: 88.11, timestamp: new Date().toISOString() }
    },
    platinum: {
      "950": { rate: 3250.75, timestamp: new Date().toISOString() }
    }
  };
}

async function fetchMetalPriceAPIRates(apiKey: string) {
  // Simulate Metal Price API integration
  return {
    gold: {
      "999": { rate: 7845.25, timestamp: new Date().toISOString() },
      "916": { rate: 7531.84, timestamp: new Date().toISOString() }
    },
    silver: {
      "999": { rate: 94.80, timestamp: new Date().toISOString() },
      "925": { rate: 87.69, timestamp: new Date().toISOString() }
    }
  };
}

async function fetchCustomAPIRates(apiKey: string, customUrl?: string, mapping?: any) {
  if (!customUrl) {
    // Fallback to simulated data if no custom URL provided
    return {
      gold: {
        "999": { rate: 7860.00, timestamp: new Date().toISOString() }
      },
      silver: {
        "999": { rate: 96.00, timestamp: new Date().toISOString() }
      }
    };
  }

  try {
    // Fetch data from custom API
    const headers: any = {
      'Content-Type': 'application/json',
    };

    // Add API key to headers if provided
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
      headers['X-API-Key'] = apiKey; // Some APIs use this format
    }

    const response = await fetch(customUrl, {
      method: 'GET',
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Apply JSON mapping to extract rates
    return mapCustomAPIResponse(data, mapping);
  } catch (error) {
    console.error('Custom API fetch error:', error);
    throw new Error(`Failed to fetch from custom API: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to map custom API response using JSON paths
function mapCustomAPIResponse(data: any, mapping: any) {
  const getValueByPath = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  const result: any = {};

  // Map gold rates
  if (mapping?.goldPath) {
    const goldRate = getValueByPath(data, mapping.goldPath);
    const goldPurity = getValueByPath(data, mapping.goldPurityPath) || "999";
    if (goldRate) {
      result.gold = {
        [goldPurity]: {
          rate: parseFloat(goldRate),
          timestamp: getValueByPath(data, mapping.timestampPath) || new Date().toISOString()
        }
      };
    }
  }

  // Map silver rates
  if (mapping?.silverPath) {
    const silverRate = getValueByPath(data, mapping.silverPath);
    const silverPurity = getValueByPath(data, mapping.silverPurityPath) || "999";
    if (silverRate) {
      result.silver = {
        [silverPurity]: {
          rate: parseFloat(silverRate),
          timestamp: getValueByPath(data, mapping.timestampPath) || new Date().toISOString()
        }
      };
    }
  }

  // Map platinum rates
  if (mapping?.platinumPath) {
    const platinumRate = getValueByPath(data, mapping.platinumPath);
    const platinumPurity = getValueByPath(data, mapping.platinumPurityPath) || "950";
    if (platinumRate) {
      result.platinum = {
        [platinumPurity]: {
          rate: parseFloat(platinumRate),
          timestamp: getValueByPath(data, mapping.timestampPath) || new Date().toISOString()
        }
      };
    }
  }

  return result;
}

export default function Rates() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"current" | "history">("current");
  const [rateMode, setRateMode] = useState<"api" | "manual">("manual"); // Default to manual
  const [apiProvider, setApiProvider] = useState<"goldapi" | "metalpriceapi" | "custom">("goldapi");
  const [apiKey, setApiKey] = useState("");
  const [customApiUrl, setCustomApiUrl] = useState("");
  const [jsonMapping, setJsonMapping] = useState({
    goldPath: "gold.rate",
    silverPath: "silver.rate",
    platinumPath: "platinum.rate",
    timestampPath: "timestamp",
    goldPurityPath: "gold.purity",
    silverPurityPath: "silver.purity",
    platinumPurityPath: "platinum.purity"
  });
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(60); // minutes
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(false);

  // Load saved API configuration on component mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('ratesApiConfig');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setApiProvider(config.apiProvider || "goldapi");
        setApiKey(config.apiKey || "");
        setCustomApiUrl(config.customApiUrl || "");
        setJsonMapping(config.jsonMapping || {
          goldPath: "gold.rate",
          silverPath: "silver.rate",
          platinumPath: "platinum.rate",
          timestampPath: "timestamp",
          goldPurityPath: "gold.purity",
          silverPurityPath: "silver.purity",
          platinumPurityPath: "platinum.purity"
        });
        setAutoRefreshInterval(config.autoRefreshInterval || 60);
        setIsAutoRefreshEnabled(config.isAutoRefreshEnabled || false);
      } catch (error) {
        console.error('Failed to load saved API configuration:', error);
      }
    }
  }, []);

  // Save API configuration to localStorage
  const saveApiConfiguration = () => {
    const config = {
      apiProvider,
      apiKey,
      customApiUrl,
      jsonMapping,
      autoRefreshInterval,
      isAutoRefreshEnabled
    };
    localStorage.setItem('ratesApiConfig', JSON.stringify(config));
    toast({
      title: "Configuration Saved",
      description: "API settings have been saved successfully",
    });
  };
  const [formData, setFormData] = useState({
    metalType: "",
    purity: "",
    ratePerGram: "",
    rateDate: new Date().toISOString().split('T')[0]
  });

  // Purity options based on metal type - Industry Standard Percentages
  const getPurityOptions = (metalType: string) => {
    switch (metalType) {
      case "gold":
        return [
          { value: "999", label: "999 (24K - 99.9%)", percentage: 99.9 },
          { value: "916", label: "916 (22K - 96.0%)", percentage: 96.0 }, // Industry standard
          { value: "750", label: "750 (18K - 75.0%)", percentage: 75.0 },
          { value: "585", label: "585 (14K - 58.5%)", percentage: 58.5 }
        ];
      case "silver":
        return [
          { value: "999", label: "999 (99.9% Pure)", percentage: 99.9 },
          { value: "925", label: "925 (92.5% Sterling)", percentage: 92.5 },
          { value: "800", label: "800 (80.0% Standard)", percentage: 80.0 }
        ];
      case "platinum":
        return [
          { value: "950", label: "950 (95.0% Standard)", percentage: 95.0 },
          { value: "900", label: "900 (90.0% Alternative)", percentage: 90.0 },
          { value: "850", label: "850 (85.0% Budget)", percentage: 85.0 }
        ];
      default:
        return [];
    }
  };
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: allRates, isLoading: isLoadingAll } = useQuery({
    queryKey: ["/api/metal-rates"],
    queryFn: fetchMetalRates,
  });

  const { data: currentRates, isLoading: isLoadingCurrent } = useQuery({
    queryKey: ["/api/metal-rates/current"],
    queryFn: fetchCurrentMetalRates,
  });

  const createRateMutation = useMutation({
    mutationFn: createMetalRate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metal-rates"] });
      queryClient.invalidateQueries({ queryKey: ["/api/metal-rates/current"] });
      setIsDialogOpen(false);
      // Reset form
      setFormData({
        metalType: "",
        purity: "",
        ratePerGram: "",
        rateDate: new Date().toISOString().split('T')[0]
      });
      toast({
        title: "Success",
        description: "Metal rate updated successfully",
      });
    },
    onError: (error) => {
      console.error("Rate update error:", error);
      toast({
        title: "Error",
        description: "Failed to update metal rate",
        variant: "destructive",
      });
    },
  });

  // API Rate Fetching Mutation
  const fetchAPIRatesMutation = useMutation({
    mutationFn: () => fetchAPIRates(apiProvider, apiKey, customApiUrl, jsonMapping),
    onSuccess: async (apiData) => {
      // Convert API data to our format and save to database
      const ratesToSave = [];

      for (const [metal, purities] of Object.entries(apiData)) {
        for (const [purity, data] of Object.entries(purities as any)) {
          ratesToSave.push({
            metalType: metal,
            purity: purity,
            ratePerGram: (data as any).rate.toString(),
            rateDate: new Date().toISOString().split('T')[0],
            source: `API-${apiProvider}`
          });
        }
      }

      // Save all rates
      for (const rate of ratesToSave) {
        await createMetalRate(rate);
      }

      toast({
        title: "Success",
        description: `Updated ${ratesToSave.length} rates from ${apiProvider.toUpperCase()} API`,
      });
    },
    onError: (error) => {
      console.error("API fetch error:", error);
      toast({
        title: "API Error",
        description: "Failed to fetch rates from API. Please check your API key and connection.",
        variant: "destructive",
      });
    },
  });

  // Auto-refresh effect
  useEffect(() => {
    if (rateMode === "api" && isAutoRefreshEnabled && apiKey) {
      const interval = setInterval(() => {
        fetchAPIRatesMutation.mutate();
      }, autoRefreshInterval * 60 * 1000); // Convert minutes to milliseconds

      return () => clearInterval(interval);
    }
  }, [rateMode, isAutoRefreshEnabled, apiKey, autoRefreshInterval]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate form data
    if (!formData.metalType || !formData.purity || !formData.ratePerGram || !formData.rateDate) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const ratePerGram = parseFloat(formData.ratePerGram);
    if (isNaN(ratePerGram) || ratePerGram <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid rate per gram",
        variant: "destructive",
      });
      return;
    }

    const data = {
      metalType: formData.metalType,
      purity: formData.purity,
      ratePerGram: ratePerGram.toString(), // Convert to string as expected by schema
      rateDate: formData.rateDate, // Keep as string, server will transform to Date
      isActive: true,
    };
    console.log("Submitting rate data:", data);
    createRateMutation.mutate(data);
  };

  const getCurrentRate = (metalType: string, purity: string) => {
    return currentRates?.find((rate: any) => 
      rate.metalType === metalType && rate.purity === purity
    );
  };

  const getPreviousRate = (metalType: string, purity: string) => {
    const rates = allRates?.filter((rate: any) => 
      rate.metalType === metalType && rate.purity === purity
    ).sort((a: any, b: any) => new Date(b.rateDate).getTime() - new Date(a.rateDate).getTime());
    return rates?.[1]; // Second most recent
  };

  const calculateChange = (current: number, previous: number) => {
    if (!previous) return { value: 0, percentage: 0, isPositive: true };
    const change = current - previous;
    const percentage = ((change / previous) * 100);
    return {
      value: Math.abs(change),
      percentage: Math.abs(percentage),
      isPositive: change >= 0
    };
  };

  const rateTypes = [
    { metalType: "gold", purity: "999", name: "Gold 24K (999)", icon: "🥇" },
    { metalType: "gold", purity: "916", name: "Gold 22K (916)", icon: "🥇" },
    { metalType: "silver", purity: "925", name: "Silver (925)", icon: "🥈" },
  ];

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Gold/Silver Rate Management" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2 bg-white rounded-lg p-1 border">
                <Button
                  variant={viewMode === "current" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("current")}
                  className={viewMode === "current" ? "bg-[hsl(154,50%,20%)] text-white" : ""}
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Current Rates
                </Button>
                <Button
                  variant={viewMode === "history" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("history")}
                  className={viewMode === "history" ? "bg-[hsl(154,50%,20%)] text-white" : ""}
                >
                  <History className="w-4 h-4 mr-2" />
                  Rate History
                </Button>
              </div>

              {/* Rate Mode Toggle */}
              <div className="flex items-center space-x-2 bg-white rounded-lg p-1 border">
                <Button
                  variant={rateMode === "api" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setRateMode("api")}
                  className={rateMode === "api" ? "bg-blue-600 text-white" : ""}
                >
                  <Globe className="w-4 h-4 mr-2" />
                  API Mode
                </Button>
                <Button
                  variant={rateMode === "manual" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setRateMode("manual")}
                  className={rateMode === "manual" ? "bg-green-600 text-white" : ""}
                >
                  <User className="w-4 h-4 mr-2" />
                  Manual Mode
                </Button>
              </div>
            </div>

            <div className="flex space-x-3">
              {/* API Mode Actions */}
              {rateMode === "api" && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => fetchAPIRatesMutation.mutate()}
                    disabled={!apiKey || fetchAPIRatesMutation.isPending}
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                  >
                    <RefreshCw className={`w-4 h-4 mr-2 ${fetchAPIRatesMutation.isPending ? 'animate-spin' : ''}`} />
                    {fetchAPIRatesMutation.isPending ? 'Fetching...' : 'Fetch API Rates'}
                  </Button>
                  <Badge variant={isAutoRefreshEnabled ? "default" : "secondary"} className="px-3 py-1">
                    <Zap className="w-3 h-3 mr-1" />
                    Auto: {isAutoRefreshEnabled ? 'ON' : 'OFF'}
                  </Badge>
                </>
              )}

              {/* Settings Button */}
              <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Rate Management Settings</DialogTitle>
                    <DialogDescription>
                      Configure API integration and rate update preferences
                    </DialogDescription>
                  </DialogHeader>

                  <Tabs defaultValue="api" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="api">API Configuration</TabsTrigger>
                      <TabsTrigger value="preferences">Preferences</TabsTrigger>
                    </TabsList>

                    <TabsContent value="api" className="space-y-4">
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="apiProvider">API Provider</Label>
                          <Select value={apiProvider} onValueChange={(value) => setApiProvider(value as typeof apiProvider)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select API provider" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="goldapi">Gold API (goldapi.io)</SelectItem>
                              <SelectItem value="metalpriceapi">Metal Price API</SelectItem>
                              <SelectItem value="custom">Custom API (Your own endpoint)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="apiKey">API Key</Label>
                          <Input
                            id="apiKey"
                            type="password"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            placeholder="Enter your API key"
                          />
                        </div>

                        {/* Custom API URL - Only show for custom provider */}
                        {apiProvider === "custom" && (
                          <div>
                            <Label htmlFor="customApiUrl">Custom API URL</Label>
                            <Input
                              id="customApiUrl"
                              type="url"
                              value={customApiUrl}
                              onChange={(e) => setCustomApiUrl(e.target.value)}
                              placeholder="https://api.example.com/rates"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Enter the full URL to your custom API endpoint
                            </p>
                          </div>
                        )}

                        <div className="flex items-center space-x-2">
                          <Switch
                            id="autoRefresh"
                            checked={isAutoRefreshEnabled}
                            onCheckedChange={setIsAutoRefreshEnabled}
                          />
                          <Label htmlFor="autoRefresh">Enable Auto Refresh</Label>
                        </div>

                        {isAutoRefreshEnabled && (
                          <div>
                            <Label htmlFor="refreshInterval">Refresh Interval (minutes)</Label>
                            <Select
                              value={autoRefreshInterval.toString()}
                              onValueChange={(value) => setAutoRefreshInterval(parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="15">15 minutes</SelectItem>
                                <SelectItem value="30">30 minutes</SelectItem>
                                <SelectItem value="60">1 hour</SelectItem>
                                <SelectItem value="120">2 hours</SelectItem>
                                <SelectItem value="240">4 hours</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {/* JSON Mapping - Only show for custom provider */}
                        {apiProvider === "custom" && (
                          <div className="space-y-4">
                            <div>
                              <Label className="text-sm font-semibold">JSON Response Mapping</Label>
                              <p className="text-xs text-gray-500 mb-3">
                                Configure how to extract rate data from your API response using dot notation (e.g., "data.gold.rate")
                              </p>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="goldPath">Gold Rate Path</Label>
                                <Input
                                  id="goldPath"
                                  value={jsonMapping.goldPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, goldPath: e.target.value}))}
                                  placeholder="gold.rate"
                                />
                              </div>
                              <div>
                                <Label htmlFor="goldPurityPath">Gold Purity Path</Label>
                                <Input
                                  id="goldPurityPath"
                                  value={jsonMapping.goldPurityPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, goldPurityPath: e.target.value}))}
                                  placeholder="gold.purity"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="silverPath">Silver Rate Path</Label>
                                <Input
                                  id="silverPath"
                                  value={jsonMapping.silverPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, silverPath: e.target.value}))}
                                  placeholder="silver.rate"
                                />
                              </div>
                              <div>
                                <Label htmlFor="silverPurityPath">Silver Purity Path</Label>
                                <Input
                                  id="silverPurityPath"
                                  value={jsonMapping.silverPurityPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, silverPurityPath: e.target.value}))}
                                  placeholder="silver.purity"
                                />
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="platinumPath">Platinum Rate Path</Label>
                                <Input
                                  id="platinumPath"
                                  value={jsonMapping.platinumPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, platinumPath: e.target.value}))}
                                  placeholder="platinum.rate"
                                />
                              </div>
                              <div>
                                <Label htmlFor="platinumPurityPath">Platinum Purity Path</Label>
                                <Input
                                  id="platinumPurityPath"
                                  value={jsonMapping.platinumPurityPath}
                                  onChange={(e) => setJsonMapping(prev => ({...prev, platinumPurityPath: e.target.value}))}
                                  placeholder="platinum.purity"
                                />
                              </div>
                            </div>

                            <div>
                              <Label htmlFor="timestampPath">Timestamp Path</Label>
                              <Input
                                id="timestampPath"
                                value={jsonMapping.timestampPath}
                                onChange={(e) => setJsonMapping(prev => ({...prev, timestampPath: e.target.value}))}
                                placeholder="timestamp"
                              />
                            </div>

                            <div className="p-3 bg-blue-50 rounded-lg">
                              <h4 className="font-semibold text-blue-800 text-sm mb-2">Example API Response:</h4>
                              <pre className="text-xs text-blue-700 bg-blue-100 p-2 rounded overflow-x-auto">
{`{
  "data": {
    "gold": {
      "rate": 7850.50,
      "purity": "999"
    },
    "silver": {
      "rate": 95.25,
      "purity": "999"
    },
    "timestamp": "2025-07-20T20:30:00Z"
  }
}`}
                              </pre>
                              <p className="text-xs text-blue-600 mt-2">
                                For this response, use paths like: <code>data.gold.rate</code>, <code>data.silver.rate</code>, <code>data.timestamp</code>
                              </p>
                            </div>

                            {/* Test API Configuration */}
                            <div className="flex space-x-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  if (!customApiUrl) {
                                    toast({
                                      title: "Validation Error",
                                      description: "Please enter a custom API URL first",
                                      variant: "destructive",
                                    });
                                    return;
                                  }

                                  try {
                                    const testResult = await fetchAPIRates("custom", apiKey, customApiUrl, jsonMapping);
                                    toast({
                                      title: "API Test Successful",
                                      description: `Found ${Object.keys(testResult).length} metal types in response`,
                                    });
                                    console.log("API Test Result:", testResult);
                                  } catch (error) {
                                    toast({
                                      title: "API Test Failed",
                                      description: error instanceof Error ? error.message : 'Unknown error',
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                              >
                                Test API Configuration
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={saveApiConfiguration}
                                className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                              >
                                Save Configuration
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </TabsContent>

                    <TabsContent value="preferences" className="space-y-4">
                      <div className="space-y-4">
                        <div>
                          <Label>Default Rate Mode</Label>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id="defaultAPI"
                                name="defaultMode"
                                checked={rateMode === "api"}
                                onChange={() => setRateMode("api")}
                              />
                              <Label htmlFor="defaultAPI">API Mode</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id="defaultManual"
                                name="defaultMode"
                                checked={rateMode === "manual"}
                                onChange={() => setRateMode("manual")}
                              />
                              <Label htmlFor="defaultManual">Manual Mode</Label>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-blue-50 rounded-lg">
                          <h4 className="font-semibold text-blue-800 mb-2">Rate Integration</h4>
                          <p className="text-sm text-blue-700">
                            Rates entered here will automatically populate across:
                          </p>
                          <ul className="text-sm text-blue-700 mt-2 space-y-1">
                            <li>• Billing system calculations</li>
                            <li>• Dashboard rate displays</li>
                            <li>• Inventory valuations</li>
                            <li>• Report generations</li>
                          </ul>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => {
                      setIsSettingsOpen(false);
                      toast({
                        title: "Settings Saved",
                        description: "Rate management settings have been updated",
                      });
                    }}>
                      Save Settings
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>

              {/* Manual Rate Entry - Only show in manual mode */}
              {rateMode === "manual" && (
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                      <Edit3 className="w-4 h-4 mr-2" />
                      Manual Entry
                    </Button>
                  </DialogTrigger>
                <DialogContent className="max-w-lg">
                  <DialogHeader>
                    <DialogTitle>Update Metal Rate</DialogTitle>
                    <DialogDescription>
                      Enter the current market rate for gold or silver. This will be used for all new transactions.
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="metalType">Metal Type</Label>
                        <Select
                          value={formData.metalType}
                          onValueChange={(value) => setFormData(prev => ({
                            ...prev,
                            metalType: value,
                            purity: "" // Reset purity when metal type changes
                          }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select metal" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gold">Gold</SelectItem>
                            <SelectItem value="silver">Silver</SelectItem>
                            <SelectItem value="platinum">Platinum</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="purity">Purity</Label>
                        <Select
                          value={formData.purity}
                          onValueChange={(value) => setFormData(prev => ({...prev, purity: value}))}
                          disabled={!formData.metalType}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={formData.metalType ? "Select purity" : "Select metal type first"} />
                          </SelectTrigger>
                          <SelectContent>
                            {getPurityOptions(formData.metalType).map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="ratePerGram">Rate per Gram (₹)</Label>
                        <Input
                          id="ratePerGram"
                          type="number"
                          step="0.01"
                          value={formData.ratePerGram}
                          onChange={(e) => setFormData(prev => ({...prev, ratePerGram: e.target.value}))}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="rateDate">Rate Date</Label>
                        <Input
                          id="rateDate"
                          type="date"
                          value={formData.rateDate}
                          onChange={(e) => setFormData(prev => ({...prev, rateDate: e.target.value}))}
                          required
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={createRateMutation.isPending}>
                        {createRateMutation.isPending ? "Updating..." : "Update Rate"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
              )}
            </div>
          </div>

          {/* Mode Status Banner */}
          <div className="mb-6">
            <Card className={`border-l-4 ${rateMode === "api" ? "border-l-blue-500 bg-blue-50" : "border-l-green-500 bg-green-50"}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {rateMode === "api" ? (
                      <Globe className="w-5 h-5 text-blue-600" />
                    ) : (
                      <User className="w-5 h-5 text-green-600" />
                    )}
                    <div>
                      <h3 className={`font-semibold ${rateMode === "api" ? "text-blue-800" : "text-green-800"}`}>
                        {rateMode === "api" ? "API Mode Active" : "Manual Mode Active"}
                      </h3>
                      <p className={`text-sm ${rateMode === "api" ? "text-blue-600" : "text-green-600"}`}>
                        {rateMode === "api"
                          ? `Rates fetched from ${apiProvider.toUpperCase()} API${isAutoRefreshEnabled ? ` • Auto-refresh: ${autoRefreshInterval}min` : ""}`
                          : "Rates managed manually • Full control over pricing"
                        }
                      </p>
                    </div>
                  </div>
                  {rateMode === "api" && (
                    <Badge variant={apiKey ? "default" : "destructive"}>
                      {apiKey ? "API Connected" : "API Key Required"}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {viewMode === "current" ? (
            /* Current Rates View */
            <div className="space-y-6">
              {/* Live Rate Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {rateTypes.map(({ metalType, purity, name, icon }) => {
                  const currentRate = getCurrentRate(metalType, purity);
                  const previousRate = getPreviousRate(metalType, purity);
                  const change = currentRate && previousRate ? 
                    calculateChange(parseFloat(currentRate.ratePerGram), parseFloat(previousRate.ratePerGram)) :
                    { value: 0, percentage: 0, isPositive: true };

                  return (
                    <Card key={`${metalType}-${purity}`} className="hover:shadow-lg transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{icon}</span>
                            <div>
                              <CardTitle className="text-lg">{name}</CardTitle>
                              <p className="text-sm text-neutral-600">per gram</p>
                            </div>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            Live
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div>
                            <p className="text-3xl font-bold text-neutral-900">
                              ₹{currentRate ? parseFloat(currentRate.ratePerGram).toLocaleString("en-IN") : "N/A"}
                            </p>
                            {currentRate && (
                              <p className="text-sm text-neutral-600">
                                Last updated: {new Date(currentRate.rateDate).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                          
                          {previousRate && (
                            <div className={`flex items-center space-x-2 ${
                              change.isPositive ? "text-green-600" : "text-red-600"
                            }`}>
                              {change.isPositive ? (
                                <TrendingUp className="w-4 h-4" />
                              ) : (
                                <TrendingDown className="w-4 h-4" />
                              )}
                              <span className="text-sm font-medium">
                                ₹{change.value.toFixed(2)} ({change.percentage.toFixed(2)}%)
                              </span>
                              <span className="text-xs text-neutral-500">today</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Market Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5" />
                    <span>Market Summary</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {(() => {
                      const goldRates = allRates?.filter((r: any) => r.metalType === "gold" && r.purity === "999") || [];
                      const todayRates = goldRates.filter((r: any) =>
                        new Date(r.rateDate).toDateString() === new Date().toDateString()
                      );
                      const todayHigh = todayRates.length > 0 ? Math.max(...todayRates.map((r: any) => parseFloat(r.ratePerGram))) : 0;
                      const todayLow = todayRates.length > 0 ? Math.min(...todayRates.map((r: any) => parseFloat(r.ratePerGram))) : 0;
                      const totalVolume = todayRates.reduce((sum: number, r: any) => sum + 0.5, 0); // Mock volume calculation
                      const currentTime = new Date().getHours();
                      const isMarketOpen = currentTime >= 9 && currentTime < 16;

                      return (
                        <>
                          <div className="text-center p-4 bg-green-50 rounded-lg">
                            <p className="text-sm text-neutral-600 mb-1">Today's High</p>
                            <p className="text-xl font-bold text-green-600">
                              {todayHigh > 0 ? `₹${todayHigh.toLocaleString("en-IN")}` : "N/A"}
                            </p>
                            <p className="text-xs text-neutral-500">Gold 24K</p>
                          </div>
                          <div className="text-center p-4 bg-red-50 rounded-lg">
                            <p className="text-sm text-neutral-600 mb-1">Today's Low</p>
                            <p className="text-xl font-bold text-red-600">
                              {todayLow > 0 ? `₹${todayLow.toLocaleString("en-IN")}` : "N/A"}
                            </p>
                            <p className="text-xs text-neutral-500">Gold 24K</p>
                          </div>
                          <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <p className="text-sm text-neutral-600 mb-1">Volume</p>
                            <p className="text-xl font-bold text-blue-600">{totalVolume.toFixed(1)}kg</p>
                            <p className="text-xs text-neutral-500">Today's trading</p>
                          </div>
                          <div className="text-center p-4 bg-[hsl(38,92%,95%)] rounded-lg">
                            <p className="text-sm text-neutral-600 mb-1">Market Status</p>
                            <p className={`text-xl font-bold ${isMarketOpen ? 'text-[hsl(38,92%,50%)]' : 'text-red-600'}`}>
                              {isMarketOpen ? "Open" : "Closed"}
                            </p>
                            <p className="text-xs text-neutral-500">
                              {isMarketOpen ? "Until 4:00 PM" : "Opens at 9:00 AM"}
                            </p>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            /* Rate History View */
            <Card>
              <CardHeader>
                <CardTitle>Rate History</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingAll ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-4 bg-neutral-200 rounded w-full mb-2"></div>
                        <div className="h-3 bg-neutral-200 rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-neutral-200">
                          <th className="text-left py-3 text-sm font-semibold text-neutral-600">Date</th>
                          <th className="text-left py-3 text-sm font-semibold text-neutral-600">Metal</th>
                          <th className="text-left py-3 text-sm font-semibold text-neutral-600">Purity</th>
                          <th className="text-right py-3 text-sm font-semibold text-neutral-600">Rate (₹/g)</th>
                          <th className="text-right py-3 text-sm font-semibold text-neutral-600">Change</th>
                          <th className="text-center py-3 text-sm font-semibold text-neutral-600">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {allRates?.map((rate: any) => {
                          const previousRate = allRates.find((r: any) => 
                            r.metalType === rate.metalType && 
                            r.purity === rate.purity && 
                            new Date(r.rateDate) < new Date(rate.rateDate)
                          );
                          const change = previousRate ? 
                            calculateChange(parseFloat(rate.ratePerGram), parseFloat(previousRate.ratePerGram)) :
                            null;

                          return (
                            <tr key={rate.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                              <td className="py-4">
                                <p className="font-medium text-neutral-800">
                                  {new Date(rate.rateDate).toLocaleDateString()}
                                </p>
                              </td>
                              <td className="py-4">
                                <div className="flex items-center space-x-2">
                                  <span>{rate.metalType === "gold" ? "🥇" : "🥈"}</span>
                                  <span className="capitalize font-medium">{rate.metalType}</span>
                                </div>
                              </td>
                              <td className="py-4">
                                <Badge variant="outline">{rate.purity}</Badge>
                              </td>
                              <td className="py-4 text-right">
                                <p className="font-semibold text-neutral-800">
                                  ₹{parseFloat(rate.ratePerGram).toLocaleString("en-IN")}
                                </p>
                              </td>
                              <td className="py-4 text-right">
                                {change ? (
                                  <div className={`flex items-center justify-end space-x-1 ${
                                    change.isPositive ? "text-green-600" : "text-red-600"
                                  }`}>
                                    {change.isPositive ? (
                                      <TrendingUp className="w-3 h-3" />
                                    ) : (
                                      <TrendingDown className="w-3 h-3" />
                                    )}
                                    <span className="text-sm">₹{change.value.toFixed(2)}</span>
                                  </div>
                                ) : (
                                  <span className="text-neutral-400 text-sm">-</span>
                                )}
                              </td>
                              <td className="py-4 text-center">
                                <Badge 
                                  variant={rate.isActive ? "default" : "secondary"}
                                  className={rate.isActive ? "bg-green-100 text-green-800" : ""}
                                >
                                  {rate.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </main>
      </div>
    </div>
  );
}