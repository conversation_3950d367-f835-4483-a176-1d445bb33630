-- JewelPro Billing Database Setup for MySQL
-- This file creates all tables and loads sample data

-- <PERSON>reate database (run this first if database doesn't exist)
-- CREATE DATABASE jewelrytracker;
-- USE jewelrytracker;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS `payments`;
DROP TABLE IF EXISTS `invoice_items`;
DROP TABLE IF EXISTS `invoices`;
DROP TABLE IF EXISTS `metal_procurement`;
DROP TABLE IF EXISTS `inventory_items`;
DROP TABLE IF EXISTS `metal_rates`;
DROP TABLE IF EXISTS `customers`;
DROP TABLE IF EXISTS `suppliers`;
DROP TABLE IF EXISTS `users`;

-- Create tables
CREATE TABLE `customers` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`contact_person` varchar(255),
	`phone` varchar(20),
	`email` varchar(255),
	`address` varchar(500),
	`gstin` varchar(15),
	`pan` varchar(10),
	`state_code` varchar(5),
	`credit_limit` decimal(12,2) DEFAULT '0',
	`outstanding_amount` decimal(12,2) DEFAULT '0',
	`discount_type` varchar(20) DEFAULT 'none',
	`discount_value` decimal(5,2) DEFAULT '0',
	`price_type` varchar(20) DEFAULT 'standard',
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `customers_id` PRIMARY KEY(`id`)
);

CREATE TABLE `inventory_items` (
	`id` int AUTO_INCREMENT NOT NULL,
	`item_code` varchar(100) NOT NULL,
	`item_name` varchar(255) NOT NULL,
	`design_name` varchar(255),
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`gross_weight` decimal(10,3) NOT NULL,
	`net_weight` decimal(10,3) NOT NULL,
	`stone_weight` decimal(10,3) DEFAULT '0',
	`wastage_percentage` decimal(5,2) DEFAULT '0',
	`making_charges` decimal(10,2) DEFAULT '0',
	`stone_charges` decimal(10,2) DEFAULT '0',
	`value_addition_percentage` decimal(5,2) DEFAULT '0',
	`hsn_code` varchar(20) NOT NULL,
	`barcode` varchar(100),
	`quantity` int DEFAULT 1,
	`is_active` boolean DEFAULT true,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `inventory_items_id` PRIMARY KEY(`id`),
	CONSTRAINT `inventory_items_item_code_unique` UNIQUE(`item_code`)
);

CREATE TABLE `invoice_items` (
	`id` int AUTO_INCREMENT NOT NULL,
	`invoice_id` int NOT NULL,
	`item_id` int NOT NULL,
	`quantity` int NOT NULL DEFAULT 1,
	`gross_weight` decimal(10,3) NOT NULL,
	`net_weight` decimal(10,3) NOT NULL,
	`rate_per_gram` decimal(10,2) NOT NULL,
	`making_charges` decimal(10,2) DEFAULT '0',
	`stone_charges` decimal(10,2) DEFAULT '0',
	`total_amount` decimal(12,2) NOT NULL,
	CONSTRAINT `invoice_items_id` PRIMARY KEY(`id`)
);

CREATE TABLE `invoices` (
	`id` int AUTO_INCREMENT NOT NULL,
	`invoice_number` varchar(100) NOT NULL,
	`customer_id` int NOT NULL,
	`invoice_date` date NOT NULL,
	`due_date` date,
	`subtotal` decimal(12,2) NOT NULL,
	`cgst_amount` decimal(10,2) DEFAULT '0',
	`sgst_amount` decimal(10,2) DEFAULT '0',
	`igst_amount` decimal(10,2) DEFAULT '0',
	`discount_amount` decimal(10,2) DEFAULT '0',
	`total_amount` decimal(12,2) NOT NULL,
	`paid_amount` decimal(12,2) DEFAULT '0',
	`balance_amount` decimal(12,2) NOT NULL,
	`status` varchar(20) NOT NULL DEFAULT 'pending',
	`payment_terms` varchar(500),
	`notes` varchar(1000),
	`eway_bill_number` varchar(50),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `invoices_id` PRIMARY KEY(`id`),
	CONSTRAINT `invoices_invoice_number_unique` UNIQUE(`invoice_number`)
);

CREATE TABLE `metal_procurement` (
	`id` int AUTO_INCREMENT NOT NULL,
	`supplier_id` int NOT NULL,
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`weight` decimal(10,3) NOT NULL,
	`purchase_rate` decimal(10,2) NOT NULL,
	`total_amount` decimal(12,2) NOT NULL,
	`invoice_number` varchar(100) NOT NULL,
	`invoice_date` date NOT NULL,
	`hsn_code` varchar(20) NOT NULL,
	`gst_amount` decimal(10,2),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `metal_procurement_id` PRIMARY KEY(`id`)
);

CREATE TABLE `metal_rates` (
	`id` int AUTO_INCREMENT NOT NULL,
	`metal_type` varchar(50) NOT NULL,
	`purity` varchar(10) NOT NULL,
	`rate_per_gram` decimal(10,2) NOT NULL,
	`rate_date` date NOT NULL,
	`is_active` boolean DEFAULT true,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `metal_rates_id` PRIMARY KEY(`id`)
);

CREATE TABLE `payments` (
	`id` int AUTO_INCREMENT NOT NULL,
	`customer_id` int NOT NULL,
	`invoice_id` int,
	`payment_date` date NOT NULL,
	`amount` decimal(12,2) NOT NULL,
	`payment_mode` varchar(50) NOT NULL,
	`reference_number` varchar(100),
	`notes` varchar(500),
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `payments_id` PRIMARY KEY(`id`)
);

CREATE TABLE `suppliers` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`contact_person` varchar(255),
	`phone` varchar(20),
	`email` varchar(255),
	`address` varchar(500),
	`gstin` varchar(15),
	`supplier_type` varchar(50) NOT NULL,
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `suppliers_id` PRIMARY KEY(`id`)
);

CREATE TABLE `users` (
	`id` int AUTO_INCREMENT NOT NULL,
	`username` varchar(255) NOT NULL,
	`password` varchar(255) NOT NULL,
	`name` varchar(255) NOT NULL,
	`role` varchar(50) NOT NULL DEFAULT 'user',
	`created_at` timestamp DEFAULT (now()),
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`)
);

-- Add foreign key constraints
ALTER TABLE `invoice_items` ADD CONSTRAINT `invoice_items_invoice_id_invoices_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE no action ON UPDATE no action;
ALTER TABLE `invoice_items` ADD CONSTRAINT `invoice_items_item_id_inventory_items_id_fk` FOREIGN KEY (`item_id`) REFERENCES `inventory_items`(`id`) ON DELETE no action ON UPDATE no action;
ALTER TABLE `invoices` ADD CONSTRAINT `invoices_customer_id_customers_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE no action ON UPDATE no action;
ALTER TABLE `metal_procurement` ADD CONSTRAINT `metal_procurement_supplier_id_suppliers_id_fk` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE no action ON UPDATE no action;
ALTER TABLE `payments` ADD CONSTRAINT `payments_customer_id_customers_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE no action ON UPDATE no action;
ALTER TABLE `payments` ADD CONSTRAINT `payments_invoice_id_invoices_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE no action ON UPDATE no action;

-- Insert sample data
-- Insert sample metal rates
INSERT INTO metal_rates (metal_type, purity, rate_per_gram, rate_date, is_active) VALUES
('gold', '999', 6845.00, CURDATE(), 1),
('gold', '916', 6273.00, CURDATE(), 1),
('silver', '925', 84.50, CURDATE(), 1);

-- Insert sample suppliers
INSERT INTO suppliers (name, contact_person, phone, email, address, gstin, supplier_type) VALUES
('Chennai Gold Imports', 'Rajesh Kumar', '+91-9876543210', '<EMAIL>', 'T. Nagar, Chennai', '33ABCDE1234F5Z6', 'bullion_dealer'),
('Tamil Nadu Refiners', 'Priya Sharma', '+91-9876543211', '<EMAIL>', 'Coimbatore', '33FGHIJ5678K9L0', 'refiner');

-- Insert sample customers
INSERT INTO customers (name, contact_person, phone, email, address, gstin, pan, state_code, credit_limit, outstanding_amount, discount_type, price_type) VALUES
('Kumar Jewellers', 'Suresh Kumar', '+91-9876543220', '<EMAIL>', 'Anna Nagar, Chennai', '33QWERT1234U5I6', '**********', '33', 500000.00, 125000.00, 'percentage', 'standard'),
('Lakshmi Ornaments', 'Lakshmi Devi', '+91-9876543221', '<EMAIL>', 'Madurai', '33YUIOP5678H9J0', '**********', '33', 300000.00, 75000.00, 'fixed', 'special'),
('Golden Palace', 'Ravi Chandran', '+91-9876543222', '<EMAIL>', 'Salem', '33ASDFG9012Q3W4', '**********', '33', 750000.00, 0.00, 'none', 'standard');

-- Insert sample inventory items
INSERT INTO inventory_items (item_code, item_name, design_name, metal_type, purity, gross_weight, net_weight, stone_weight, wastage_percentage, making_charges, stone_charges, value_addition_percentage, hsn_code, barcode, quantity) VALUES
('GC001', 'Gold Chain 22K', 'Traditional Chain', 'gold', '916', 25.500, 24.000, 0.000, 6.00, 2400.00, 0.00, 15.00, '71131900', 'BC001', 12),
('GB002', 'Gold Bangles Set', 'Floral Design', 'gold', '916', 45.750, 42.500, 1.250, 7.50, 4250.00, 5000.00, 20.00, '71131900', 'BC002', 8),
('SR003', 'Silver Ring', 'Contemporary', 'silver', '925', 8.200, 7.800, 0.400, 5.00, 780.00, 1200.00, 10.00, '71131900', 'BC003', 25),
('GE004', 'Gold Earrings', 'Antique Style', 'gold', '916', 12.300, 11.500, 0.800, 6.50, 1150.00, 3500.00, 18.00, '71131900', 'BC004', 6),
('SN005', 'Silver Necklace', 'Modern Design', 'silver', '925', 35.600, 33.200, 2.400, 7.00, 3320.00, 8000.00, 12.00, '71131900', 'BC005', 4);

-- Insert sample invoices
INSERT INTO invoices (invoice_number, customer_id, invoice_date, due_date, subtotal, cgst_amount, sgst_amount, discount_amount, total_amount, paid_amount, balance_amount, status, payment_terms, notes) VALUES
('INV-2024-1001', 1, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 25 DAY), 156750.00, 2351.25, 2351.25, 0.00, 161452.50, 161452.50, 0.00, 'paid', 'net30', 'Bulk order - 5% discount applied'),
('INV-2024-1002', 2, DATE_SUB(CURDATE(), INTERVAL 3 DAY), DATE_ADD(CURDATE(), INTERVAL 27 DAY), 287500.00, 4312.50, 4312.50, 14375.00, 281750.00, 150000.00, 131750.00, 'pending', 'net30', 'Special customer pricing'),
('INV-2024-1003', 3, DATE_SUB(CURDATE(), INTERVAL 1 DAY), DATE_ADD(CURDATE(), INTERVAL 29 DAY), 95420.00, 1431.30, 1431.30, 0.00, 98282.60, 0.00, 98282.60, 'pending', 'net30', '');

-- Insert sample payments
INSERT INTO payments (customer_id, invoice_id, payment_date, amount, payment_mode, reference_number, notes) VALUES
(1, 1, DATE_SUB(CURDATE(), INTERVAL 3 DAY), 161452.50, 'rtgs', 'RTGS240120001', 'Full payment received'),
(2, 2, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 150000.00, 'bank', 'CHQ240120002', 'Partial payment - cheque');

-- Insert sample metal procurement
INSERT INTO metal_procurement (supplier_id, metal_type, purity, weight, purchase_rate, total_amount, invoice_number, invoice_date, hsn_code, gst_amount) VALUES
(1, 'gold', '999', 500.000, 6800.00, 3400000.00, 'SUP-001-2024', DATE_SUB(CURDATE(), INTERVAL 7 DAY), '********', 51000.00),
(2, 'silver', '925', 2000.000, 82.50, 165000.00, 'SUP-002-2024', DATE_SUB(CURDATE(), INTERVAL 5 DAY), '********', 2475.00);

-- Database setup complete!
SELECT 'Database setup completed successfully!' as status;
