# JewelPro Billing - Jewelry Wholesaler Management System

## Overview

JewelPro Billing is a comprehensive web application designed specifically for jewelry wholesalers in Tamil Nadu. The system manages the complete business flow from metal procurement to billing, inventory management, and customer relationships. The application follows a modern full-stack architecture with React frontend, Node.js/Express backend, and PostgreSQL database using Drizzle ORM.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The application follows a monorepo structure with clear separation between client, server, and shared components:

```
├── client/          # React frontend with Vite
├── server/          # Express.js backend
├── shared/          # Shared types and database schema
└── migrations/      # Database migration files
```

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query for server state management
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database ORM**: Drizzle ORM for type-safe database operations
- **API Pattern**: RESTful API with JSON responses
- **Error Handling**: Centralized error handling middleware

## Key Components

### Database Schema (`shared/schema.ts`)
The application manages jewelry business operations through several core entities:
- **Users**: System user management with role-based access
- **Suppliers**: Metal procurement vendor management
- **Customers**: Customer relationship management
- **Metal Procurement**: Raw material purchase tracking
- **Inventory Items**: Jewelry product catalog
- **Invoices & Invoice Items**: Billing and sales management
- **Payments**: Payment tracking and ledger management
- **Metal Rates**: Dynamic pricing for gold/silver rates

### Frontend Pages
- **Dashboard**: Overview with key business metrics
- **Inventory**: Product catalog and stock management
- **Billing**: Invoice creation and management
- **Customers**: Customer database and relationship management
- **Payments**: Payment tracking (placeholder for future implementation)
- **Rates**: Metal rate management (placeholder for future implementation)
- **Reports**: Analytics and reporting (placeholder for future implementation)
- **Settings**: System configuration (placeholder for future implementation)

### UI Components
The application uses a consistent design system built on:
- **shadcn/ui**: Pre-built accessible components
- **Radix UI**: Headless component primitives
- **Custom Components**: Business-specific components like BillingModal
- **Layout Components**: Sidebar navigation and header with real-time metal rates

## Data Flow

### Client-Server Communication
1. **API Requests**: Frontend uses `apiRequest` utility for HTTP communication
2. **Query Management**: TanStack Query handles caching, refetching, and error states
3. **Type Safety**: Shared TypeScript types ensure consistency between client and server
4. **Real-time Updates**: Optimistic updates and cache invalidation for immediate UI feedback

### Database Operations
1. **Connection Management**: Neon serverless PostgreSQL with connection pooling
2. **Query Building**: Drizzle ORM provides type-safe query construction
3. **Schema Management**: Database migrations managed through drizzle-kit
4. **Data Validation**: Zod schemas validate data at both API and database levels

## External Dependencies

### Core Framework Dependencies
- **React Ecosystem**: React, React DOM, React Hook Form
- **Backend**: Express.js, Node.js
- **Database**: Neon serverless PostgreSQL, Drizzle ORM
- **Build Tools**: Vite, TypeScript, ESBuild

### UI and Styling
- **Component Library**: Radix UI primitives, shadcn/ui components
- **Styling**: Tailwind CSS with custom design tokens
- **Icons**: Lucide React icon library
- **Utilities**: clsx, tailwind-merge for conditional styling

### Development Tools
- **Type Safety**: TypeScript, Zod for runtime validation
- **Code Quality**: ESLint, Prettier (implied by modern setup)
- **Development**: Vite dev server, hot module replacement

## Deployment Strategy

### Build Process
1. **Frontend Build**: Vite builds optimized React application to `dist/public`
2. **Backend Build**: ESBuild bundles server code to `dist/index.js`
3. **Database**: Drizzle migrations apply schema changes to PostgreSQL

### Environment Configuration
- **Development**: Uses Vite dev server with Express backend
- **Production**: Serves static files from Express with API routes
- **Database**: Neon serverless PostgreSQL with connection string configuration

### Deployment Requirements
- **Node.js Environment**: ES modules support required
- **Environment Variables**: `DATABASE_URL` for PostgreSQL connection
- **Static Assets**: Frontend built files served from Express
- **Database Setup**: Automated migrations through drizzle-kit

The application is designed for scalability with serverless database architecture and modern deployment practices suitable for platforms like Vercel, Railway, or similar Node.js hosting providers.