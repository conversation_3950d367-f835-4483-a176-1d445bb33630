/**
 * Jewelry Calculation Engine
 * Based on the reference calculations provided
 */

export interface JewelryCalculationInput {
  grossWeight: number;
  stoneWeight: number;
  purityPercentage: number;
  goldRatePerGram: number;
  wastagePercentage: number;
  makingChargesRate: number;
  stoneAmount: number;
}

export interface JewelryCalculationResult {
  // Basic calculations
  netWeight: number;           // Gross Weight - Stone Weight
  fineWeight: number;          // Net Weight × (Purity % ÷ 100)
  goldValue: number;           // Fine Weight × Gold Rate
  makingCharges: number;       // Net Weight × MC Rate
  itemTotal: number;           // Gold Value + MC + Stone Amount
  
  // GST calculations
  taxableAmount: number;       // Item Total ÷ 1.03
  cgstAmount: number;          // Taxable Amount × 0.015
  sgstAmount: number;          // Taxable Amount × 0.015
  grandTotal: number;          // Taxable Amount + CGST + SGST
  
  // Breakdown for display
  breakdown: {
    grossWeight: number;
    stoneWeight: number;
    netWeight: number;
    fineWeight: number;
    goldValue: number;
    wastagePercentage: number;
    makingCharges: number;
    stoneAmount: number;
    itemTotal: number;
    taxableAmount: number;
    cgstAmount: number;
    sgstAmount: number;
    grandTotal: number;
  };
}

/**
 * Calculate all jewelry values based on input parameters
 */
export function calculateJewelryValues(input: JewelryCalculationInput): JewelryCalculationResult {
  // Step 1: Calculate Net Weight
  const netWeight = input.grossWeight - input.stoneWeight;
  
  // Step 2: Calculate Fine Weight
  const fineWeight = netWeight * (input.purityPercentage / 100);
  
  // Step 3: Calculate Gold Value
  const goldValue = fineWeight * input.goldRatePerGram;
  
  // Step 4: Calculate Making Charges
  const makingCharges = netWeight * input.makingChargesRate;
  
  // Step 5: Calculate Item Total
  const itemTotal = goldValue + makingCharges + input.stoneAmount;
  
  // Step 6: Calculate Taxable Amount (Item Total ÷ 1.03)
  const taxableAmount = itemTotal / 1.03;
  
  // Step 7: Calculate GST amounts
  const cgstAmount = taxableAmount * 0.015; // 1.5%
  const sgstAmount = taxableAmount * 0.015; // 1.5%
  
  // Step 8: Calculate Grand Total
  const grandTotal = taxableAmount + cgstAmount + sgstAmount;
  
  return {
    netWeight: roundToDecimals(netWeight, 3),
    fineWeight: roundToDecimals(fineWeight, 3),
    goldValue: roundToDecimals(goldValue, 2),
    makingCharges: roundToDecimals(makingCharges, 2),
    itemTotal: roundToDecimals(itemTotal, 2),
    taxableAmount: roundToDecimals(taxableAmount, 2),
    cgstAmount: roundToDecimals(cgstAmount, 2),
    sgstAmount: roundToDecimals(sgstAmount, 2),
    grandTotal: roundToDecimals(grandTotal, 2),
    breakdown: {
      grossWeight: roundToDecimals(input.grossWeight, 3),
      stoneWeight: roundToDecimals(input.stoneWeight, 3),
      netWeight: roundToDecimals(netWeight, 3),
      fineWeight: roundToDecimals(fineWeight, 3),
      goldValue: roundToDecimals(goldValue, 2),
      wastagePercentage: roundToDecimals(input.wastagePercentage, 2),
      makingCharges: roundToDecimals(makingCharges, 2),
      stoneAmount: roundToDecimals(input.stoneAmount, 2),
      itemTotal: roundToDecimals(itemTotal, 2),
      taxableAmount: roundToDecimals(taxableAmount, 2),
      cgstAmount: roundToDecimals(cgstAmount, 2),
      sgstAmount: roundToDecimals(sgstAmount, 2),
      grandTotal: roundToDecimals(grandTotal, 2),
    }
  };
}

/**
 * Calculate multiple items and provide summary
 */
export function calculateInvoiceTotals(items: JewelryCalculationResult[]): {
  totalTaxableAmount: number;
  totalCgstAmount: number;
  totalSgstAmount: number;
  grandTotal: number;
} {
  const totalTaxableAmount = items.reduce((sum, item) => sum + item.taxableAmount, 0);
  const totalCgstAmount = items.reduce((sum, item) => sum + item.cgstAmount, 0);
  const totalSgstAmount = items.reduce((sum, item) => sum + item.sgstAmount, 0);
  const grandTotal = items.reduce((sum, item) => sum + item.grandTotal, 0);
  
  return {
    totalTaxableAmount: roundToDecimals(totalTaxableAmount, 2),
    totalCgstAmount: roundToDecimals(totalCgstAmount, 2),
    totalSgstAmount: roundToDecimals(totalSgstAmount, 2),
    grandTotal: roundToDecimals(grandTotal, 2),
  };
}

/**
 * Helper function to round numbers to specified decimal places
 */
function roundToDecimals(value: number, decimals: number): number {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format weight for display
 */
export function formatWeight(weight: number): string {
  return `${weight.toFixed(3)} g`;
}

/**
 * Format percentage for display
 */
export function formatPercentage(percentage: number): string {
  return `${percentage.toFixed(2)}%`;
}
