# Custom API Integration Guide

## Overview

The Jewelry Tracker system now supports custom API integration for metal rates, allowing you to connect to any third-party API endpoint to automatically fetch and update metal prices.

## Features

- ✅ **Custom API URL**: Connect to any REST API endpoint
- ✅ **JSON Path Mapping**: Flexible mapping of API response fields
- ✅ **Multi-Metal Support**: Gold, Silver, and Platinum rates
- ✅ **Authentication**: API key support with multiple header formats
- ✅ **Configuration Persistence**: Save and load API settings
- ✅ **API Testing**: Built-in test functionality
- ✅ **Error Handling**: Comprehensive error reporting

## Getting Started

### 1. Access API Settings

1. Navigate to **Rates** page
2. Toggle to **API Mode** (top-right switch)
3. Click **Settings** button
4. Go to **API Configuration** tab

### 2. Configure Custom API

1. **Select Provider**: Choose "Custom API (Your own endpoint)"
2. **API URL**: Enter your API endpoint URL
3. **API Key**: Enter your authentication key (optional)
4. **JSON Mapping**: Configure field mappings (see below)

### 3. JSON Path Mapping

Configure how to extract data from your API response using dot notation:

#### Available Fields:
- **Gold Rate Path**: Path to gold price (e.g., `data.metals.gold.rate`)
- **Gold Purity Path**: Path to gold purity (e.g., `data.metals.gold.purity`)
- **Silver Rate Path**: Path to silver price (e.g., `data.metals.silver.rate`)
- **Silver Purity Path**: Path to silver purity (e.g., `data.metals.silver.purity`)
- **Platinum Rate Path**: Path to platinum price (e.g., `data.metals.platinum.rate`)
- **Platinum Purity Path**: Path to platinum purity (e.g., `data.metals.platinum.purity`)
- **Timestamp Path**: Path to timestamp (e.g., `data.timestamp`)

## API Response Examples

### Example 1: Nested Structure
```json
{
  "status": "success",
  "data": {
    "metals": {
      "gold": {
        "rate": 7850.50,
        "purity": "999"
      },
      "silver": {
        "rate": 95.25,
        "purity": "999"
      }
    },
    "timestamp": "2025-07-20T20:30:00Z"
  }
}
```

**Mapping Configuration:**
- Gold Rate Path: `data.metals.gold.rate`
- Gold Purity Path: `data.metals.gold.purity`
- Silver Rate Path: `data.metals.silver.rate`
- Silver Purity Path: `data.metals.silver.purity`
- Timestamp Path: `data.timestamp`

### Example 2: Flat Structure
```json
{
  "gold_price": 7850.50,
  "silver_price": 95.25,
  "gold_purity": "999",
  "silver_purity": "999",
  "last_updated": "2025-07-20T20:30:00Z"
}
```

**Mapping Configuration:**
- Gold Rate Path: `gold_price`
- Gold Purity Path: `gold_purity`
- Silver Rate Path: `silver_price`
- Silver Purity Path: `silver_purity`
- Timestamp Path: `last_updated`

## Authentication

The system supports multiple authentication methods:

### API Key in Headers
- **Authorization**: `Bearer YOUR_API_KEY`
- **X-API-Key**: `YOUR_API_KEY`

Both headers are automatically added when you provide an API key.

## Testing Your Configuration

1. **Configure Settings**: Set up your API URL and mappings
2. **Test API**: Click "Test API Configuration" button
3. **Review Results**: Check console for detailed response data
4. **Save Configuration**: Click "Save Configuration" to persist settings

## Mock API for Testing

A mock API endpoint is available for testing purposes:

**URL**: `http://localhost:5000/api/mock-rates`

**Response Structure**:
```json
{
  "status": "success",
  "data": {
    "metals": {
      "gold": {
        "rate": 7850.50,
        "purity": "999",
        "unit": "per_gram",
        "currency": "INR"
      },
      "silver": {
        "rate": 95.25,
        "purity": "999",
        "unit": "per_gram",
        "currency": "INR"
      },
      "platinum": {
        "rate": 3200.00,
        "purity": "950",
        "unit": "per_gram",
        "currency": "INR"
      }
    },
    "timestamp": "2025-07-20T20:30:00.000Z",
    "source": "Mock API Provider"
  }
}
```

**Recommended Mapping for Mock API**:
- Gold Rate Path: `data.metals.gold.rate`
- Gold Purity Path: `data.metals.gold.purity`
- Silver Rate Path: `data.metals.silver.rate`
- Silver Purity Path: `data.metals.silver.purity`
- Platinum Rate Path: `data.metals.platinum.rate`
- Platinum Purity Path: `data.metals.platinum.purity`
- Timestamp Path: `data.timestamp`

## Troubleshooting

### Common Issues

1. **"API Test Failed" Error**
   - Check if API URL is accessible
   - Verify API key is correct
   - Ensure API returns valid JSON

2. **"Found 0 metal types" Message**
   - Review JSON path mappings
   - Check if paths match actual API response structure
   - Use browser developer tools to inspect API response

3. **Authentication Errors**
   - Verify API key format
   - Check if API requires specific authentication method
   - Review API documentation for auth requirements

### Debug Tips

1. **Use Browser Developer Tools**:
   - Open Network tab
   - Test API configuration
   - Inspect actual API response

2. **Check Console Logs**:
   - Look for "API Test Result" logs
   - Review error messages for details

3. **Validate JSON Paths**:
   - Use online JSON path testers
   - Verify dot notation syntax

## Best Practices

1. **Save Configurations**: Always save working configurations
2. **Test Before Use**: Test API configuration before enabling auto-refresh
3. **Monitor API Limits**: Be aware of API rate limits
4. **Backup Settings**: Export/backup your configuration
5. **Error Handling**: Monitor for API failures and have fallback plans

## Support

For additional support or custom integration requirements, please refer to the main documentation or contact the development team.
