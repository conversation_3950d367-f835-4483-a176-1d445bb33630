# MySQL Setup Guide for JewelPro Billing

## Overview

The JewelPro Billing application has been successfully converted from PostgreSQL to MySQL. This guide will help you set up MySQL and get the application running with a real database.

## Current Status

✅ **Application Converted to MySQL**
- Database schema converted from PostgreSQL to MySQL
- Drizzle ORM configured for MySQL
- Sample data updated for MySQL syntax
- Application runs in demo mode without database

## MySQL Setup Options

### Option 1: Local MySQL Installation

1. **Install MySQL Server**
   ```bash
   # Windows (using Chocolatey)
   choco install mysql

   # Or download from: https://dev.mysql.com/downloads/mysql/
   ```

2. **Start MySQL Service**
   ```bash
   # Windows
   net start mysql
   ```

3. **Create Database and User**
   ```sql
   mysql -u root -p
   CREATE DATABASE jewelrytracker;
   CREATE USER 'jewelry_user'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON jewelrytracker.* TO 'jewelry_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Update .env file**
   ```env
   DATABASE_URL="mysql://jewelry_user:your_secure_password@localhost:3306/jewelrytracker"
   ```

### Option 2: Docker MySQL

1. **Run MySQL in Docker**
   ```bash
   docker run --name mysql-jewelry \
     -e MYSQL_ROOT_PASSWORD=rootpassword \
     -e MYSQL_DATABASE=jewelrytracker \
     -e MYSQL_USER=jewelry_user \
     -e MYSQL_PASSWORD=userpassword \
     -p 3306:3306 \
     -d mysql:8.0
   ```

2. **Update .env file**
   ```env
   DATABASE_URL="mysql://jewelry_user:userpassword@localhost:3306/jewelrytracker"
   ```

### Option 3: Cloud MySQL (Recommended for Production)

Popular cloud MySQL services:
- **PlanetScale** (MySQL-compatible, serverless)
- **AWS RDS MySQL**
- **Google Cloud SQL**
- **Azure Database for MySQL**

## Database Migration

Once you have MySQL running:

1. **Push Database Schema**
   ```bash
   npm run db:push
   ```

2. **Load Sample Data**
   ```bash
   mysql -u jewelry_user -p jewelrytracker < sample_data.sql
   ```

## Verification

1. **Check Database Connection**
   - Start the application: `npm run dev`
   - Look for: `✅ Connected to MySQL database`

2. **Test Application Features**
   - Open http://localhost:5000
   - Navigate through different sections
   - Try creating customers, inventory items, etc.

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure MySQL service is running
   - Check port 3306 is not blocked

2. **Access Denied**
   - Verify username/password in DATABASE_URL
   - Check user permissions in MySQL

3. **Database Not Found**
   - Create the database: `CREATE DATABASE jewelrytracker;`

### Demo Mode

If you can't set up MySQL immediately, the application runs in demo mode:
- ⚠️ Data won't persist
- ⚠️ Database operations return empty results
- ✅ UI and frontend features work normally

## Next Steps

1. Set up MySQL using one of the options above
2. Update the DATABASE_URL in .env
3. Run database migrations
4. Load sample data
5. Start developing!

## Schema Changes

The application schema has been converted from PostgreSQL to MySQL:
- `serial` → `int AUTO_INCREMENT`
- `text` → `varchar` with appropriate lengths
- `pgTable` → `mysqlTable`
- Boolean values: `true/false` → `1/0`
- Date functions: `CURRENT_DATE` → `CURDATE()`

All functionality remains the same, just with MySQL as the backend database.
