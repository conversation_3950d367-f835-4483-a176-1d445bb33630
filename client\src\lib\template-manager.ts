import { InvoiceTemplate, TemplatePreview, CreateTemplateRequest, UpdateTemplateRequest, TemplateColors, TemplateLayout, TemplateSettings } from '../types/template';

/**
 * Template Manager for Invoice Templates
 * Handles CRUD operations and template management
 */
export class TemplateManager {
  private static instance: TemplateManager;
  private templates: Map<string, InvoiceTemplate> = new Map();
  private defaultTemplateId: string | null = null;

  private constructor() {
    this.initializeDefaultTemplates();
  }

  public static getInstance(): TemplateManager {
    if (!TemplateManager.instance) {
      TemplateManager.instance = new TemplateManager();
    }
    return TemplateManager.instance;
  }

  /**
   * Initialize default templates
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates = this.getBuiltInTemplates();
    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
      if (template.isDefault) {
        this.defaultTemplateId = template.id;
      }
    });
  }

  /**
   * Get all built-in templates
   */
  private getBuiltInTemplates(): InvoiceTemplate[] {
    return [
      {
        id: 'modern-blue',
        name: 'Modern Blue',
        description: 'Clean and modern template with blue accent colors',
        isDefault: true,
        isActive: true,
        colors: {
          primary: [41, 128, 185],
          accent: [39, 174, 96],
          dark: [44, 62, 80],
          lightGray: [236, 240, 241],
          mediumGray: [149, 165, 166]
        },
        layout: {
          paperSize: 'a4' as const,
          orientation: 'portrait' as const,
          margin: 6,
          headerHeight: 28,
          sectionSpacing: 8,
          fontSize: {
            header: 18,
            subheader: 12,
            body: 8,
            small: 6
          }
        },
        settings: {
          showLogo: true,
          showWatermark: false,
          showGSTBreakdown: false,
          showBankDetails: true,
          showTermsAndConditions: true,
          compactMode: true,
          currency: '₹',
          dateFormat: 'DD/MM/YYYY',
          numberFormat: 'en-IN'
        },
        customFields: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      },
      {
        id: 'luxury-gold',
        name: 'Luxury Gold',
        description: 'Premium template with gold accents for high-end jewelry',
        isDefault: false,
        isActive: true,
        colors: {
          primary: [212, 175, 55],
          accent: [184, 134, 11],
          dark: [92, 57, 0],
          lightGray: [254, 252, 232],
          mediumGray: [161, 138, 78]
        },
        layout: {
          paperSize: 'a4' as const,
          orientation: 'portrait' as const,
          margin: 8,
          headerHeight: 35,
          sectionSpacing: 10,
          fontSize: {
            header: 20,
            subheader: 14,
            body: 9,
            small: 7
          }
        },
        settings: {
          showLogo: true,
          showWatermark: true,
          showGSTBreakdown: false,
          showBankDetails: true,
          showTermsAndConditions: true,
          compactMode: false,
          currency: '₹',
          dateFormat: 'DD/MM/YYYY',
          numberFormat: 'en-IN'
        },
        customFields: {
          watermarkText: 'PREMIUM JEWELRY'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      },
      {
        id: 'minimal-gray',
        name: 'Minimal Gray',
        description: 'Clean minimal design with gray tones',
        isDefault: false,
        isActive: true,
        colors: {
          primary: [75, 85, 99],
          accent: [107, 114, 128],
          dark: [31, 41, 55],
          lightGray: [249, 250, 251],
          mediumGray: [156, 163, 175]
        },
        layout: {
          paperSize: 'a4' as const,
          orientation: 'portrait' as const,
          margin: 5,
          headerHeight: 25,
          sectionSpacing: 6,
          fontSize: {
            header: 16,
            subheader: 10,
            body: 7,
            small: 5
          }
        },
        settings: {
          showLogo: false,
          showWatermark: false,
          showGSTBreakdown: false,
          showBankDetails: true,
          showTermsAndConditions: false,
          compactMode: true,
          currency: '₹',
          dateFormat: 'DD/MM/YYYY',
          numberFormat: 'en-IN'
        },
        customFields: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      },
      {
        id: 'classic-green',
        name: 'Classic Green',
        description: 'Traditional template with green color scheme',
        isDefault: false,
        isActive: true,
        colors: {
          primary: [34, 139, 34],
          accent: [50, 205, 50],
          dark: [0, 100, 0],
          lightGray: [240, 255, 240],
          mediumGray: [144, 238, 144]
        },
        layout: {
          paperSize: 'a4' as const,
          orientation: 'portrait' as const,
          margin: 10,
          headerHeight: 40,
          sectionSpacing: 12,
          fontSize: {
            header: 22,
            subheader: 16,
            body: 10,
            small: 8
          }
        },
        settings: {
          showLogo: true,
          showWatermark: false,
          showGSTBreakdown: true,
          showBankDetails: true,
          showTermsAndConditions: true,
          compactMode: false,
          currency: '₹',
          dateFormat: 'DD/MM/YYYY',
          numberFormat: 'en-IN'
        },
        customFields: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      }
    ];
  }

  /**
   * Get all templates
   */
  public getAllTemplates(): InvoiceTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get template previews
   */
  public getTemplatePreviews(): TemplatePreview[] {
    return Array.from(this.templates.values()).map(template => ({
      id: template.id,
      name: template.name,
      thumbnail: this.generateThumbnail(template),
      description: template.description,
      isDefault: template.isDefault
    }));
  }

  /**
   * Get template by ID
   */
  public getTemplate(id: string): InvoiceTemplate | null {
    return this.templates.get(id) || null;
  }

  /**
   * Get default template
   */
  public getDefaultTemplate(): InvoiceTemplate | null {
    if (this.defaultTemplateId) {
      return this.templates.get(this.defaultTemplateId) || null;
    }
    return null;
  }

  /**
   * Create new template
   */
  public createTemplate(request: CreateTemplateRequest): InvoiceTemplate {
    const id = this.generateId();
    const template: InvoiceTemplate = {
      id,
      name: request.name,
      description: request.description,
      isDefault: false,
      isActive: true,
      colors: request.colors,
      layout: request.layout,
      settings: request.settings,
      customFields: request.customFields || {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'user'
    };

    this.templates.set(id, template);
    return template;
  }

  /**
   * Update existing template
   */
  public updateTemplate(request: UpdateTemplateRequest): InvoiceTemplate | null {
    const existing = this.templates.get(request.id);
    if (!existing) {
      return null;
    }

    const updated: InvoiceTemplate = {
      ...existing,
      ...request,
      updatedAt: new Date().toISOString()
    };

    this.templates.set(request.id, updated);
    return updated;
  }

  /**
   * Delete template
   */
  public deleteTemplate(id: string): boolean {
    const template = this.templates.get(id);
    if (!template || template.createdBy === 'system') {
      return false; // Cannot delete system templates
    }

    return this.templates.delete(id);
  }

  /**
   * Set default template
   */
  public setDefaultTemplate(id: string): boolean {
    const template = this.templates.get(id);
    if (!template) {
      return false;
    }

    // Remove default from current default
    if (this.defaultTemplateId) {
      const currentDefault = this.templates.get(this.defaultTemplateId);
      if (currentDefault) {
        currentDefault.isDefault = false;
        this.templates.set(this.defaultTemplateId, currentDefault);
      }
    }

    // Set new default
    template.isDefault = true;
    this.templates.set(id, template);
    this.defaultTemplateId = id;

    return true;
  }

  /**
   * Clone template
   */
  public cloneTemplate(id: string, newName: string): InvoiceTemplate | null {
    const original = this.templates.get(id);
    if (!original) {
      return null;
    }

    const cloned: InvoiceTemplate = {
      ...original,
      id: this.generateId(),
      name: newName,
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'user'
    };

    this.templates.set(cloned.id, cloned);
    return cloned;
  }

  /**
   * Export template
   */
  public exportTemplate(id: string): string | null {
    const template = this.templates.get(id);
    if (!template) {
      return null;
    }

    return JSON.stringify(template, null, 2);
  }

  /**
   * Import template
   */
  public importTemplate(templateJson: string): InvoiceTemplate | null {
    try {
      const template = JSON.parse(templateJson) as InvoiceTemplate;
      
      // Validate template structure
      if (!this.validateTemplate(template)) {
        return null;
      }

      // Generate new ID and update metadata
      template.id = this.generateId();
      template.isDefault = false;
      template.createdAt = new Date().toISOString();
      template.updatedAt = new Date().toISOString();
      template.createdBy = 'user';

      this.templates.set(template.id, template);
      return template;
    } catch (error) {
      console.error('Failed to import template:', error);
      return null;
    }
  }

  /**
   * Search templates
   */
  public searchTemplates(query: string): InvoiceTemplate[] {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.templates.values()).filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery)
    );
  }

  /**
   * Get templates by category
   */
  public getTemplatesByCategory(category: string): InvoiceTemplate[] {
    return Array.from(this.templates.values()).filter(template =>
      template.customFields?.category === category
    );
  }

  /**
   * Validate template structure
   */
  private validateTemplate(template: any): boolean {
    return (
      template &&
      typeof template.name === 'string' &&
      typeof template.description === 'string' &&
      template.colors &&
      template.layout &&
      template.settings
    );
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return 'template_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Generate thumbnail for template
   */
  private generateThumbnail(template: InvoiceTemplate): string {
    // Generate a simple SVG thumbnail based on template colors
    const { primary, accent } = template.colors;
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="30" fill="rgb(${primary.join(',')})"/>
        <rect x="10" y="40" width="180" height="20" fill="rgb(${accent.join(',')})"/>
        <rect x="10" y="70" width="90" height="60" fill="#f0f0f0" stroke="rgb(${primary.join(',')})"/>
        <rect x="110" y="70" width="80" height="60" fill="#f8f8f8" stroke="rgb(${accent.join(',')})"/>
        <text x="100" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12">${template.name}</text>
      </svg>
    `)}`;
  }

  /**
   * Reset to default templates
   */
  public resetToDefaults(): void {
    this.templates.clear();
    this.defaultTemplateId = null;
    this.initializeDefaultTemplates();
  }

  /**
   * Get template statistics
   */
  public getTemplateStats(): {
    total: number;
    active: number;
    custom: number;
    system: number;
  } {
    const templates = Array.from(this.templates.values());
    return {
      total: templates.length,
      active: templates.filter(t => t.isActive).length,
      custom: templates.filter(t => t.createdBy === 'user').length,
      system: templates.filter(t => t.createdBy === 'system').length
    };
  }
}

// Export singleton instance
export const templateManager = TemplateManager.getInstance();