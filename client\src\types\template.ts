export interface TemplateColors {
  primary: [number, number, number];
  accent: [number, number, number];
  dark: [number, number, number];
  lightGray: [number, number, number];
  mediumGray: [number, number, number];
}

export interface TemplateLayout {
  paperSize: 'a4' | 'a5' | 'letter' | 'legal' | 'a3'; // Paper size options
  orientation: 'portrait' | 'landscape'; // Paper orientation
  margin: number; // in mm
  headerHeight: number; // in mm
  sectionSpacing: number; // in mm
  fontSize: {
    header: number; // in pt
    subheader: number; // in pt
    body: number; // in pt
    small: number; // in pt
  };
}

export interface TemplateSettings {
  showLogo: boolean;
  showWatermark: boolean;
  showGSTBreakdown: boolean;
  showBankDetails: boolean;
  showTermsAndConditions: boolean;
  compactMode: boolean;
  currency: string;
  dateFormat: string;
  numberFormat: string;
}

export interface InvoiceTemplate {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  isActive: boolean;
  colors: TemplateColors;
  layout: TemplateLayout;
  settings: TemplateSettings;
  customFields: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface TemplatePreview {
  id: string;
  name: string;
  thumbnail: string;
  description: string;
  isDefault: boolean;
}

export type TemplateCategory = 'modern' | 'classic' | 'minimal' | 'luxury' | 'custom';

// Paper size definitions (width x height in mm)
export const PAPER_SIZES = {
  a4: { width: 210, height: 297, label: 'A4 (210 × 297 mm)' },
  a5: { width: 148, height: 210, label: 'A5 (148 × 210 mm)' },
  letter: { width: 216, height: 279, label: 'Letter (8.5 × 11 in)' },
  legal: { width: 216, height: 356, label: 'Legal (8.5 × 14 in)' },
  a3: { width: 297, height: 420, label: 'A3 (297 × 420 mm)' }
} as const;

export type PaperSize = keyof typeof PAPER_SIZES;
export type PaperOrientation = 'portrait' | 'landscape';

// Helper function to get paper dimensions considering orientation
export const getPaperDimensions = (size: PaperSize, orientation: PaperOrientation) => {
  const { width, height } = PAPER_SIZES[size];
  return orientation === 'landscape' 
    ? { width: height, height: width }
    : { width, height };
};

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: TemplateCategory;
  colors: TemplateColors;
  layout: TemplateLayout;
  settings: TemplateSettings;
  customFields?: Record<string, any>;
}

export interface UpdateTemplateRequest extends Partial<CreateTemplateRequest> {
  id: string;
}