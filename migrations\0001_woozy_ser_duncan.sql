ALTER TABLE `inventory_items` ADD `purity_percentage` decimal(5,2) NOT NULL;--> statement-breakpoint
ALTER TABLE `inventory_items` ADD `fine_weight` decimal(10,3) NOT NULL;--> statement-breakpoint
ALTER TABLE `inventory_items` ADD `stone_amount` decimal(10,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `inventory_items` ADD `making_charges_rate` decimal(10,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `fine_weight` decimal(10,3) NOT NULL;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `purity_percentage` decimal(5,2) NOT NULL;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `stone_weight` decimal(10,3) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `stone_amount` decimal(10,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `wastage_percentage` decimal(5,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `gold_value` decimal(12,2) NOT NULL;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `making_charges_rate` decimal(10,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `item_total` decimal(12,2) NOT NULL;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `taxable_amount` decimal(12,2) NOT NULL;--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `cgst_amount` decimal(10,2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE `invoice_items` ADD `sgst_amount` decimal(10,2) DEFAULT '0';