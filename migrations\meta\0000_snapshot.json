{"version": "5", "dialect": "mysql", "id": "425aeb7e-e0db-4e47-b546-5a9a93b4df6d", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"customers": {"name": "customers", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "gstin": {"name": "gstin", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false, "autoincrement": false}, "pan": {"name": "pan", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "state_code": {"name": "state_code", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit_limit": {"name": "credit_limit", "type": "decimal(12,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "outstanding_amount": {"name": "outstanding_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "discount_type": {"name": "discount_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'none'"}, "discount_value": {"name": "discount_value", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "price_type": {"name": "price_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'standard'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"customers_id": {"name": "customers_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "inventory_items": {"name": "inventory_items", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "item_code": {"name": "item_code", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "item_name": {"name": "item_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "design_name": {"name": "design_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "metal_type": {"name": "metal_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "purity": {"name": "purity", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "gross_weight": {"name": "gross_weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": true, "autoincrement": false}, "net_weight": {"name": "net_weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": true, "autoincrement": false}, "stone_weight": {"name": "stone_weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "wastage_percentage": {"name": "wastage_percentage", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "making_charges": {"name": "making_charges", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "stone_charges": {"name": "stone_charges", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "value_addition_percentage": {"name": "value_addition_percentage", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "hsn_code": {"name": "hsn_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "barcode": {"name": "barcode", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "quantity": {"name": "quantity", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"inventory_items_id": {"name": "inventory_items_id", "columns": ["id"]}}, "uniqueConstraints": {"inventory_items_item_code_unique": {"name": "inventory_items_item_code_unique", "columns": ["item_code"]}}, "checkConstraint": {}}, "invoice_items": {"name": "invoice_items", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "invoice_id": {"name": "invoice_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "item_id": {"name": "item_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "gross_weight": {"name": "gross_weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": true, "autoincrement": false}, "net_weight": {"name": "net_weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rate_per_gram": {"name": "rate_per_gram", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "making_charges": {"name": "making_charges", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "stone_charges": {"name": "stone_charges", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"invoice_items_invoice_id_invoices_id_fk": {"name": "invoice_items_invoice_id_invoices_id_fk", "tableFrom": "invoice_items", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invoice_items_item_id_inventory_items_id_fk": {"name": "invoice_items_item_id_inventory_items_id_fk", "tableFrom": "invoice_items", "tableTo": "inventory_items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"invoice_items_id": {"name": "invoice_items_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "invoices": {"name": "invoices", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "invoice_date": {"name": "invoice_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false, "autoincrement": false}, "subtotal": {"name": "subtotal", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "cgst_amount": {"name": "cgst_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "sgst_amount": {"name": "sgst_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "igst_amount": {"name": "igst_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "discount_amount": {"name": "discount_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "paid_amount": {"name": "paid_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0'"}, "balance_amount": {"name": "balance_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "payment_terms": {"name": "payment_terms", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false, "autoincrement": false}, "eway_bill_number": {"name": "eway_bill_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"invoices_customer_id_customers_id_fk": {"name": "invoices_customer_id_customers_id_fk", "tableFrom": "invoices", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"invoices_id": {"name": "invoices_id", "columns": ["id"]}}, "uniqueConstraints": {"invoices_invoice_number_unique": {"name": "invoices_invoice_number_unique", "columns": ["invoice_number"]}}, "checkConstraint": {}}, "metal_procurement": {"name": "metal_procurement", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "supplier_id": {"name": "supplier_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "metal_type": {"name": "metal_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "purity": {"name": "purity", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "weight": {"name": "weight", "type": "decimal(10,3)", "primaryKey": false, "notNull": true, "autoincrement": false}, "purchase_rate": {"name": "purchase_rate", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_amount": {"name": "total_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "invoice_date": {"name": "invoice_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "hsn_code": {"name": "hsn_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "gst_amount": {"name": "gst_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"metal_procurement_supplier_id_suppliers_id_fk": {"name": "metal_procurement_supplier_id_suppliers_id_fk", "tableFrom": "metal_procurement", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"metal_procurement_id": {"name": "metal_procurement_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "metal_rates": {"name": "metal_rates", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "metal_type": {"name": "metal_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "purity": {"name": "purity", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rate_per_gram": {"name": "rate_per_gram", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rate_date": {"name": "rate_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"metal_rates_id": {"name": "metal_rates_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "customer_id": {"name": "customer_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "invoice_id": {"name": "invoice_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_date": {"name": "payment_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_mode": {"name": "payment_mode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reference_number": {"name": "reference_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"payments_customer_id_customers_id_fk": {"name": "payments_customer_id_customers_id_fk", "tableFrom": "payments", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_invoice_id_invoices_id_fk": {"name": "payments_invoice_id_invoices_id_fk", "tableFrom": "payments", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"payments_id": {"name": "payments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "suppliers": {"name": "suppliers", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "gstin": {"name": "gstin", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false, "autoincrement": false}, "supplier_type": {"name": "supplier_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"suppliers_id": {"name": "suppliers_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}