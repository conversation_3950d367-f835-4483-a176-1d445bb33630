-- Create templates table for invoice templates
CREATE TABLE IF NOT EXISTS templates (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description VARCHAR(500),
  category VARCHAR(20) NOT NULL DEFAULT 'custom',
  colors JSON NOT NULL,
  layout JSON NOT NULL,
  settings JSON NOT NULL,
  custom_fields JSON,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_by VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default system templates
INSERT INTO templates (id, name, description, category, colors, layout, settings, custom_fields, is_default, is_active, created_by) VALUES
(
  'modern-blue',
  'Modern Blue',
  'Clean and modern template with blue accent colors',
  'modern',
  J<PERSON><PERSON>_OBJECT(
    'primary', JSO<PERSON>_ARRAY(41, 128, 185),
    'accent', J<PERSON><PERSON>_ARRAY(39, 174, 96),
    'dark', JSO<PERSON>_ARRAY(44, 62, 80),
    'lightGray', JSON_ARRAY(236, 240, 241),
    'mediumGray', JSON_ARRAY(149, 165, 166)
  ),
  JSON_OBJECT(
    'paperSize', 'a4',
    'orientation', 'portrait',
    'margin', 6,
    'headerHeight', 28,
    'sectionSpacing', 8,
    'fontSize', JSON_OBJECT(
      'header', 18,
      'subheader', 12,
      'body', 8,
      'small', 6
    )
  ),
  JSON_OBJECT(
    'showLogo', true,
    'showWatermark', false,
    'showGSTBreakdown', false,
    'showBankDetails', true,
    'showTermsAndConditions', true,
    'compactMode', true,
    'currency', '₹',
    'dateFormat', 'DD/MM/YYYY',
    'numberFormat', 'en-IN'
  ),
  JSON_OBJECT(),
  true,
  true,
  'system'
),
(
  'luxury-gold',
  'Luxury Gold',
  'Premium template with gold accents for high-end jewelry',
  'luxury',
  JSON_OBJECT(
    'primary', JSON_ARRAY(212, 175, 55),
    'accent', JSON_ARRAY(184, 134, 11),
    'dark', JSON_ARRAY(92, 57, 0),
    'lightGray', JSON_ARRAY(254, 252, 232),
    'mediumGray', JSON_ARRAY(161, 138, 78)
  ),
  JSON_OBJECT(
    'paperSize', 'a4',
    'orientation', 'portrait',
    'margin', 8,
    'headerHeight', 35,
    'sectionSpacing', 10,
    'fontSize', JSON_OBJECT(
      'header', 20,
      'subheader', 14,
      'body', 9,
      'small', 7
    )
  ),
  JSON_OBJECT(
    'showLogo', true,
    'showWatermark', true,
    'showGSTBreakdown', false,
    'showBankDetails', true,
    'showTermsAndConditions', true,
    'compactMode', false,
    'currency', '₹',
    'dateFormat', 'DD/MM/YYYY',
    'numberFormat', 'en-IN'
  ),
  JSON_OBJECT('watermarkText', 'PREMIUM JEWELRY'),
  false,
  true,
  'system'
),
(
  'minimal-gray',
  'Minimal Gray',
  'Clean minimal design with gray tones',
  'minimal',
  JSON_OBJECT(
    'primary', JSON_ARRAY(75, 85, 99),
    'accent', JSON_ARRAY(107, 114, 128),
    'dark', JSON_ARRAY(31, 41, 55),
    'lightGray', JSON_ARRAY(249, 250, 251),
    'mediumGray', JSON_ARRAY(156, 163, 175)
  ),
  JSON_OBJECT(
    'paperSize', 'a4',
    'orientation', 'portrait',
    'margin', 5,
    'headerHeight', 25,
    'sectionSpacing', 6,
    'fontSize', JSON_OBJECT(
      'header', 16,
      'subheader', 10,
      'body', 7,
      'small', 5
    )
  ),
  JSON_OBJECT(
    'showLogo', false,
    'showWatermark', false,
    'showGSTBreakdown', false,
    'showBankDetails', true,
    'showTermsAndConditions', false,
    'compactMode', true,
    'currency', '₹',
    'dateFormat', 'DD/MM/YYYY',
    'numberFormat', 'en-IN'
  ),
  JSON_OBJECT(),
  false,
  true,
  'system'
),
(
  'classic-green',
  'Classic Green',
  'Traditional template with green color scheme',
  'classic',
  JSON_OBJECT(
    'primary', JSON_ARRAY(34, 139, 34),
    'accent', JSON_ARRAY(50, 205, 50),
    'dark', JSON_ARRAY(0, 100, 0),
    'lightGray', JSON_ARRAY(240, 255, 240),
    'mediumGray', JSON_ARRAY(144, 238, 144)
  ),
  JSON_OBJECT(
    'margin', 10,
    'headerHeight', 40,
    'sectionSpacing', 12,
    'fontSize', JSON_OBJECT(
      'header', 22,
      'subheader', 16,
      'body', 10,
      'small', 8
    )
  ),
  JSON_OBJECT(
    'showLogo', true,
    'showWatermark', false,
    'showGSTBreakdown', true,
    'showBankDetails', true,
    'showTermsAndConditions', true,
    'compactMode', false,
    'currency', '₹',
    'dateFormat', 'DD/MM/YYYY',
    'numberFormat', 'en-IN'
  ),
  JSON_OBJECT(),
  false,
  true,
  'system'
);

-- Create index for better performance
CREATE INDEX idx_templates_active ON templates(is_active);
CREATE INDEX idx_templates_default ON templates(is_default);
CREATE INDEX idx_templates_created_by ON templates(created_by);