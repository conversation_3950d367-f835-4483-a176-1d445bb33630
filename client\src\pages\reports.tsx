import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { BarChart3, TrendingUp, FileText, Download, Calendar, Filter, Pie<PERSON><PERSON>, Line<PERSON><PERSON> } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart as RechartsPieChart, Pie, Cell, LineChart as RechartsLineChart, Line } from "recharts";
import { apiRequest } from "@/lib/queryClient";

async function fetchReportData(type: string, dateRange: string, startDate?: string, endDate?: string) {
  const params = new URLSearchParams({
    type,
    dateRange,
    ...(startDate && { startDate }),
    ...(endDate && { endDate })
  });
  const response = await apiRequest("GET", `/api/reports?${params}`);
  return response.json();
}

export default function Reports() {
  const [reportType, setReportType] = useState("sales");
  const [dateRange, setDateRange] = useState("30");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const { data: reportData, isLoading } = useQuery({
    queryKey: ["/api/reports", reportType, dateRange, startDate, endDate],
    queryFn: () => fetchReportData(reportType, dateRange, startDate, endDate),
  });

  // Mock data for demonstration
  const salesData = [
    { month: 'Jan', sales: 450000, orders: 25 },
    { month: 'Feb', sales: 520000, orders: 32 },
    { month: 'Mar', sales: 680000, orders: 41 },
    { month: 'Apr', sales: 590000, orders: 35 },
    { month: 'May', sales: 720000, orders: 48 },
    { month: 'Jun', sales: 650000, orders: 39 },
  ];

  const inventoryData = [
    { category: 'Gold Chains', value: 35, count: 45 },
    { category: 'Gold Rings', value: 25, count: 62 },
    { category: 'Gold Earrings', value: 20, count: 38 },
    { category: 'Silver Items', value: 15, count: 85 },
    { category: 'Bangles', value: 5, count: 12 },
  ];

  const customerData = [
    { name: 'Kumar Jewellers', orders: 15, value: 450000 },
    { name: 'Lakshmi Ornaments', orders: 12, value: 380000 },
    { name: 'Golden Palace', orders: 8, value: 290000 },
    { name: 'Royal Gems', orders: 6, value: 220000 },
    { name: 'Sree Jewels', orders: 5, value: 180000 },
  ];

  const metalRatesData = [
    { date: '2024-01-15', gold24K: 6820, gold22K: 6250, silver: 82 },
    { date: '2024-01-16', gold24K: 6835, gold22K: 6264, silver: 83 },
    { date: '2024-01-17', gold24K: 6845, gold22K: 6273, silver: 84 },
    { date: '2024-01-18', gold24K: 6860, gold22K: 6287, silver: 85 },
    { date: '2024-01-19', gold24K: 6875, gold22K: 6301, silver: 86 },
    { date: '2024-01-20', gold24K: 6845, gold22K: 6273, silver: 84.5 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const reportTypes = [
    { value: "sales", label: "Sales Report", icon: BarChart3 },
    { value: "inventory", label: "Inventory Report", icon: PieChart },
    { value: "customers", label: "Customer Analysis", icon: TrendingUp },
    { value: "rates", label: "Metal Rates Trend", icon: LineChart },
    { value: "financial", label: "Financial Summary", icon: FileText },
  ];

  const exportReport = (format: string) => {
    if (!reportData) return;

    if (format === "pdf") {
      // Generate PDF report
      const reportContent = generateReportHTML();
      const blob = new Blob([reportContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (format === "excel") {
      // Generate CSV for Excel compatibility
      const csvContent = generateCSV();
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const generateReportHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .summary { margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report</h1>
          <p>Generated on: ${new Date().toLocaleDateString()}</p>
        </div>
        <div class="content">
          ${generateReportTableHTML()}
        </div>
      </body>
      </html>
    `;
  };

  const generateReportTableHTML = () => {
    if (reportType === "sales" && reportData?.sales) {
      return `
        <table>
          <thead>
            <tr><th>Date</th><th>Total Sales</th><th>Orders</th><th>Avg Order Value</th></tr>
          </thead>
          <tbody>
            ${reportData.sales.map((item: any) => `
              <tr>
                <td>${new Date(item.date).toLocaleDateString()}</td>
                <td>₹${parseFloat(item.totalSales || 0).toLocaleString('en-IN')}</td>
                <td>${item.orderCount}</td>
                <td>₹${parseFloat(item.avgOrderValue || 0).toLocaleString('en-IN')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    }
    return "<p>No data available for export</p>";
  };

  const generateCSV = () => {
    if (reportType === "sales" && reportData?.sales) {
      const headers = "Date,Total Sales,Orders,Avg Order Value\n";
      const rows = reportData.sales.map((item: any) =>
        `${new Date(item.date).toLocaleDateString()},${item.totalSales || 0},${item.orderCount || 0},${item.avgOrderValue || 0}`
      ).join('\n');
      return headers + rows;
    }
    return "No data available";
  };

  const renderSalesReport = () => {
    const salesChartData = reportData?.sales?.map((item: any) => ({
      date: new Date(item.date).toLocaleDateString(),
      sales: parseFloat(item.totalSales || 0),
      orders: parseInt(item.orderCount || 0)
    })) || salesData;

    const totalSales = reportData?.sales?.reduce((sum: number, item: any) => sum + parseFloat(item.totalSales || 0), 0) || 4110000;
    const totalOrders = reportData?.sales?.reduce((sum: number, item: any) => sum + parseInt(item.orderCount || 0), 0) || 220;
    const avgOrderValue = totalOrders > 0 ? totalSales / totalOrders : 18700;

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Sales Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={salesChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  name === 'sales' ? `₹${value.toLocaleString('en-IN')}` : value,
                  name === 'sales' ? 'Sales' : 'Orders'
                ]} />
                <Bar dataKey="sales" fill="#22c55e" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">₹{(totalSales / 100000).toFixed(1)}L</p>
                <p className="text-sm text-neutral-600">Total Sales</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{totalOrders}</p>
                <p className="text-sm text-neutral-600">Total Orders</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">₹{(avgOrderValue / 1000).toFixed(1)}K</p>
                <p className="text-sm text-neutral-600">Avg Order Value</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">+12.5%</p>
                <p className="text-sm text-neutral-600">Growth Rate</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  const renderInventoryReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Inventory Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={inventoryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {inventoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stock Levels by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={inventoryData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderCustomerReport = () => {
    const customers = reportData?.customers || customerData.map(c => ({
      customerName: c.name,
      totalOrders: c.orders,
      totalValue: c.value
    }));

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Customers by Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {customers.map((customer: any, index: number) => (
                <div key={customer.customerName} className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[hsl(154,50%,20%)] text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-semibold text-neutral-900">{customer.customerName}</p>
                      <p className="text-sm text-neutral-600">{customer.totalOrders} orders</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-neutral-900">₹{parseFloat(customer.totalValue || 0).toLocaleString('en-IN')}</p>
                    <p className="text-sm text-neutral-600">Total Value</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderRatesReport = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Metal Rates Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <RechartsLineChart data={metalRatesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${value}`, 'Rate per gram']} />
              <Line type="monotone" dataKey="gold24K" stroke="#FFD700" strokeWidth={2} name="Gold 24K" />
              <Line type="monotone" dataKey="gold22K" stroke="#FFA500" strokeWidth={2} name="Gold 22K" />
              <Line type="monotone" dataKey="silver" stroke="#C0C0C0" strokeWidth={2} name="Silver" />
            </RechartsLineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderFinancialReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-neutral-600">This Month</p>
                <p className="text-2xl font-bold text-green-600">₹7.2L</p>
              </div>
              <div>
                <p className="text-sm text-neutral-600">Last Month</p>
                <p className="text-lg text-neutral-800">₹6.5L</p>
              </div>
              <div className="flex items-center space-x-2 text-green-600">
                <TrendingUp className="w-4 h-4" />
                <span className="text-sm font-medium">+10.8% growth</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Outstanding</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-neutral-600">Total Outstanding</p>
                <p className="text-2xl font-bold text-red-600">₹2.3L</p>
              </div>
              <div>
                <p className="text-sm text-neutral-600">Overdue</p>
                <p className="text-lg text-red-500">₹45K</p>
              </div>
              <div>
                <Badge variant="destructive" className="text-xs">3 overdue accounts</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Profit Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-neutral-600">Gross Margin</p>
                <p className="text-2xl font-bold text-blue-600">22.5%</p>
              </div>
              <div>
                <p className="text-sm text-neutral-600">Net Margin</p>
                <p className="text-lg text-blue-500">18.2%</p>
              </div>
              <div className="flex items-center space-x-2 text-blue-600">
                <TrendingUp className="w-4 h-4" />
                <span className="text-sm font-medium">+2.1% vs last month</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderReportContent = () => {
    switch (reportType) {
      case "sales":
        return renderSalesReport();
      case "inventory":
        return renderInventoryReport();
      case "customers":
        return renderCustomerReport();
      case "rates":
        return renderRatesReport();
      case "financial":
        return renderFinancialReport();
      default:
        return renderSalesReport();
    }
  };

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Reports & Analytics" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Report Controls */}
          <div className="bg-white rounded-lg border p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div>
                  <Label htmlFor="reportType">Report Type</Label>
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {reportTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center space-x-2">
                            <type.icon className="w-4 h-4" />
                            <span>{type.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="dateRange">Date Range</Label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">Last 7 days</SelectItem>
                      <SelectItem value="30">Last 30 days</SelectItem>
                      <SelectItem value="90">Last 3 months</SelectItem>
                      <SelectItem value="365">Last year</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {dateRange === "custom" && (
                  <>
                    <div>
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="w-40"
                      />
                    </div>
                    <div>
                      <Label htmlFor="endDate">End Date</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="w-40"
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="flex space-x-3">
                <Button variant="outline" onClick={() => exportReport("pdf")}>
                  <Download className="w-4 h-4 mr-2" />
                  Export PDF
                </Button>
                <Button variant="outline" onClick={() => exportReport("excel")}>
                  <Download className="w-4 h-4 mr-2" />
                  Export Excel
                </Button>
              </div>
            </div>
          </div>

          {/* Report Content */}
          {isLoading ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-6 bg-neutral-200 rounded w-1/4 mb-4"></div>
                      <div className="h-32 bg-neutral-200 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            renderReportContent()
          )}
        </main>
      </div>
    </div>
  );
}