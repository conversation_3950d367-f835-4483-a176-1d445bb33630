// Helper functions to prepare invoice data with company settings and current metal rates
import { CompanySettings, getCompanySettingsSync } from '@/hooks/useCompanySettings';

interface MetalRates {
  gold: number;
  silver: number;
  date: string;
}

// Load company settings (now uses the centralized hook's sync function)
export const getCompanySettings = (): CompanySettings => {
  return getCompanySettingsSync();
};

// Get current metal rates (you can integrate with your API or use stored rates)
export const getCurrentMetalRates = async (): Promise<MetalRates> => {
  try {
    // Try to fetch from your API first
    const response = await fetch('/api/metal-rates/current');
    if (response.ok) {
      const rates = await response.json();
      return {
        gold: rates.gold || 7800,
        silver: rates.silver || 950,
        date: new Date().toLocaleDateString('en-IN')
      };
    }
  } catch (error) {
    console.error('Error fetching metal rates:', error);
  }

  // Return default rates if API fails
  return {
    gold: 7800,
    silver: 950,
    date: new Date().toLocaleDateString('en-IN')
  };
};

// Enhanced invoice data preparation
export const prepareInvoiceData = async (invoiceData: any) => {
  const companySettings = getCompanySettings();
  const metalRates = await getCurrentMetalRates();

  // Enhance the invoice data with company settings and metal rates
  const enhancedData = {
    ...invoiceData,
    company: companySettings,
    metalRates: metalRates,
    // Ensure all required fields are present with defaults
    invoice: {
      ...invoiceData.invoice,
      igstAmount: invoiceData.invoice.igstAmount || '0',
      ewayBillNumber: invoiceData.invoice.ewayBillNumber || '',
      paymentTerms: invoiceData.invoice.paymentTerms || 'Net 30 days',
    },
    customer: {
      ...invoiceData.customer,
      gstin: invoiceData.customer.gstin || '',
      pan: invoiceData.customer.pan || '',
      stateCode: invoiceData.customer.stateCode || companySettings.stateCode,
    },
    // Enhance items with calculated fields
    items: invoiceData.items?.map((item: any) => ({
      ...item,
      fineWeight: item.fineWeight || calculateFineWeight(item.netWeight, item.purityPercentage),
      stoneWeight: item.stoneWeight || calculateStoneWeight(item.grossWeight, item.netWeight),
      goldValue: item.goldValue || calculateGoldValue(item.fineWeight || calculateFineWeight(item.netWeight, item.purityPercentage), item.ratePerGram),
      taxableAmount: item.taxableAmount || calculateTaxableAmount(item.itemTotal || item.totalAmount),
      cgstAmount: item.cgstAmount || calculateCGST(item.taxableAmount || calculateTaxableAmount(item.itemTotal || item.totalAmount)),
      sgstAmount: item.sgstAmount || calculateSGST(item.taxableAmount || calculateTaxableAmount(item.itemTotal || item.totalAmount)),
      hsnCode: item.hsnCode || '7113', // Default HSN for jewelry
      metalType: item.metalType || 'Gold',
      purity: item.purity || '22K',
      purityPercentage: item.purityPercentage || '91.6',
      wastagePercentage: item.wastagePercentage || '10',
      stoneAmount: item.stoneAmount || '0',
      stoneCharges: item.stoneCharges || '0',
    })) || []
  };

  return enhancedData;
};

// Helper calculation functions
const calculateFineWeight = (netWeight: string, purityPercentage: string): string => {
  const net = parseFloat(netWeight || '0');
  const purity = parseFloat(purityPercentage || '91.6');
  return (net * (purity / 100)).toFixed(3);
};

const calculateStoneWeight = (grossWeight: string, netWeight: string): string => {
  const gross = parseFloat(grossWeight || '0');
  const net = parseFloat(netWeight || '0');
  return Math.max(0, gross - net).toFixed(3);
};

const calculateGoldValue = (fineWeight: string, ratePerGram: string): string => {
  const fine = parseFloat(fineWeight || '0');
  const rate = parseFloat(ratePerGram || '0');
  return (fine * rate).toFixed(2);
};

const calculateTaxableAmount = (totalAmount: string): string => {
  const total = parseFloat(totalAmount || '0');
  // Assuming total includes 3% GST, so taxable = total / 1.03
  return (total / 1.03).toFixed(2);
};

const calculateCGST = (taxableAmount: string): string => {
  const taxable = parseFloat(taxableAmount || '0');
  return (taxable * 0.015).toFixed(2); // 1.5% CGST
};

const calculateSGST = (taxableAmount: string): string => {
  const taxable = parseFloat(taxableAmount || '0');
  return (taxable * 0.015).toFixed(2); // 1.5% SGST
};

// State codes for GST compliance
export const INDIAN_STATE_CODES = {
  '01': 'Jammu and Kashmir',
  '02': 'Himachal Pradesh',
  '03': 'Punjab',
  '04': 'Chandigarh',
  '05': 'Uttarakhand',
  '06': 'Haryana',
  '07': 'Delhi',
  '08': 'Rajasthan',
  '09': 'Uttar Pradesh',
  '10': 'Bihar',
  '11': 'Sikkim',
  '12': 'Arunachal Pradesh',
  '13': 'Nagaland',
  '14': 'Manipur',
  '15': 'Mizoram',
  '16': 'Tripura',
  '17': 'Meghalaya',
  '18': 'Assam',
  '19': 'West Bengal',
  '20': 'Jharkhand',
  '21': 'Odisha',
  '22': 'Chhattisgarh',
  '23': 'Madhya Pradesh',
  '24': 'Gujarat',
  '25': 'Daman and Diu',
  '26': 'Dadra and Nagar Haveli',
  '27': 'Maharashtra',
  '28': 'Andhra Pradesh',
  '29': 'Karnataka',
  '30': 'Goa',
  '31': 'Lakshadweep',
  '32': 'Kerala',
  '33': 'Tamil Nadu',
  '34': 'Puducherry',
  '35': 'Andaman and Nicobar Islands',
  '36': 'Telangana',
  '37': 'Andhra Pradesh (New)',
  '38': 'Ladakh'
};

// HSN codes for jewelry items
export const JEWELRY_HSN_CODES = {
  'gold_jewelry': '7113',
  'silver_jewelry': '7113',
  'platinum_jewelry': '7113',
  'precious_stones': '7103',
  'imitation_jewelry': '7117',
  'gold_bars': '7108',
  'silver_bars': '7106'
};