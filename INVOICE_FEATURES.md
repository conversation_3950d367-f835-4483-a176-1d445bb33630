# GST-Compliant Jewelry Invoice System

## Overview
This system provides comprehensive GST-compliant invoice generation specifically designed for jewelry businesses, with multiple PDF formats and professional layouts.

## Key Features Implemented

### 1. GST Compliance
- **GSTIN Validation**: Proper GSTIN format validation and display
- **HSN Codes**: Jewelry-specific HSN codes (7113 for jewelry items)
- **Tax Calculations**: Accurate CGST (1.5%) and SGST (1.5%) calculations
- **State Code Management**: Proper state code handling for inter/intra-state transactions
- **GST Breakdown Table**: Detailed HSN-wise GST summary as per compliance requirements

### 2. Jewelry-Specific Features
- **Weight Management**: Gross weight, net weight, stone weight, fine weight tracking
- **Purity Calculations**: Gold purity percentage and fine weight calculations
- **Wastage Handling**: Wastage percentage and weight calculations
- **Stone Charges**: Separate stone weight and charges tracking
- **Making Charges**: Detailed making charges per item
- **Metal Rates**: Current gold/silver rates display with date
- **Multiple Metal Types**: Support for Gold, Silver, Platinum jewelry

### 3. Professional Invoice Formats

#### A. GST-Compliant Invoice (`gst-jewelry-invoice.ts`)
- **Full GST Compliance**: Complete tax invoice format as per GST rules
- **Detailed Item Table**: 20+ columns including all jewelry-specific fields
- **Weight Summary**: Comprehensive weight breakdown
- **GST Breakdown**: HSN-wise tax summary table
- **Bank Details**: Complete banking information for payments
- **Terms & Conditions**: Standard jewelry business terms
- **Amount in Words**: Indian currency conversion
- **Professional Layout**: Clean, business-ready design

#### B. Professional Invoice (`professional-pdf-generator.ts`)
- **Enhanced Design**: Modern, professional appearance
- **Jewelry Focus**: Optimized for jewelry business needs
- **Detailed Calculations**: Clear breakdown of all charges
- **Customer-Friendly**: Easy to read and understand

#### C. Simple Invoice (`simple-pdf-generator.ts`)
- **Quick Generation**: Fast, lightweight PDF creation
- **Essential Information**: Core invoice details only
- **Fallback Option**: Reliable backup format

### 4. Company Settings Management
- **Complete Company Profile**: Name, address, contact details
- **GST Information**: GSTIN, PAN, State Code with validation
- **Bank Details**: Complete banking information
- **Settings Persistence**: Local storage + database sync
- **Validation**: Real-time format validation for GSTIN, PAN, IFSC

### 5. Enhanced User Interface
- **Multiple Format Options**: Dropdown menus for different PDF formats
- **Download Options**: 
  - GST Compliant Invoice
  - Professional Format
  - Simple Format
  - Advanced Format
- **Print Options**: Direct print functionality for all formats
- **Format Selection**: Easy switching between invoice types

### 6. Data Enhancement System
- **Automatic Calculations**: Auto-calculate missing fields
- **Metal Rate Integration**: Current market rates inclusion
- **Default Values**: Sensible defaults for missing data
- **Data Validation**: Ensure all required fields are present

## Technical Implementation

### File Structure
```
client/src/lib/
├── gst-jewelry-invoice.ts          # Main GST-compliant generator
├── professional-pdf-generator.ts   # Professional format
├── simple-pdf-generator.ts         # Simple format
├── pdf-generator.ts                # Advanced format
└── invoice-data-helper.ts          # Data preparation utilities

client/src/components/billing/
└── company-settings.tsx            # Company settings management

client/src/pages/
├── billing.tsx                     # Enhanced with multiple formats
└── settings.tsx                    # Integrated company settings
```

### Key Classes and Functions

#### GSTJewelryInvoiceGenerator
- `generateGSTInvoice()`: Creates complete GST-compliant PDF
- `printGSTInvoice()`: Opens print dialog
- Professional border and layout design
- Comprehensive jewelry item tables
- GST breakdown tables
- Amount calculations and conversions

#### Data Helper Functions
- `prepareInvoiceData()`: Enhances raw data with calculations
- `getCompanySettings()`: Loads company configuration
- `getCurrentMetalRates()`: Fetches current metal prices
- Automatic field calculations for missing data

### GST Compliance Features
1. **Tax Invoice Format**: Proper "TAX INVOICE" header
2. **Company Details**: Complete GSTIN, PAN, address
3. **Customer Details**: Billing and shipping information
4. **Item Details**: HSN codes, quantities, rates, taxes
5. **Tax Calculations**: Separate CGST/SGST or IGST
6. **Tax Summary**: HSN-wise tax breakdown table
7. **Amount in Words**: Legal requirement compliance
8. **Terms & Conditions**: Standard business terms

### Jewelry Business Specific
1. **Weight Tracking**: All weight types (gross, net, stone, fine)
2. **Purity Management**: Gold purity percentages
3. **Wastage Calculations**: Industry-standard wastage handling
4. **Making Charges**: Separate making charge calculations
5. **Stone Charges**: Gemstone and diamond charges
6. **Metal Rates**: Current market rate display
7. **BIS Compliance**: Hallmarking information

## Usage Instructions

### 1. Configure Company Settings
1. Go to Settings → Company tab
2. Fill in all company details including GSTIN, PAN
3. Add bank details for payment information
4. Save settings (stored locally and in database)

### 2. Generate Invoices
1. Go to Billing page
2. Select an invoice
3. Choose format from dropdown:
   - **GST Compliant**: Full legal compliance
   - **Professional**: Enhanced business format
   - **Simple**: Quick basic format
4. Download or Print as needed

### 3. Print Options
- Each format has optimized print layouts
- Professional borders and spacing
- Print-friendly fonts and sizes

## Compliance Standards

### GST Requirements Met
- ✅ Proper tax invoice format
- ✅ GSTIN display and validation
- ✅ HSN code classification
- ✅ Tax rate specification (1.5% CGST + 1.5% SGST)
- ✅ Taxable value calculations
- ✅ Tax amount breakdowns
- ✅ Total amount in words
- ✅ Place of supply indication
- ✅ Invoice numbering system

### Jewelry Industry Standards
- ✅ Weight specifications (gross, net, stone)
- ✅ Purity percentages and fine weight
- ✅ Making charges transparency
- ✅ Stone charge specifications
- ✅ Wastage calculations
- ✅ Current metal rate display
- ✅ BIS hallmarking references

## Future Enhancements
1. **E-Way Bill Integration**: Automatic e-way bill generation
2. **Digital Signatures**: PDF signing capabilities
3. **Multi-language Support**: Regional language invoices
4. **Barcode Generation**: Item and invoice barcodes
5. **Email Integration**: Direct email sending
6. **Template Customization**: Custom invoice templates
7. **Batch Processing**: Multiple invoice generation

## Testing
- Use the test file `test-pdf.html` for basic PDF testing
- All formats tested with sample jewelry data
- GST calculations verified for accuracy
- Print layouts optimized for A4 paper

This implementation provides a complete, professional, GST-compliant invoicing solution specifically tailored for jewelry businesses in India.