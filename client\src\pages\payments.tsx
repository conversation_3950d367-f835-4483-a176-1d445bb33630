import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Search, CreditCard, Receipt, Eye, DollarSign, Calendar, User } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { fetchCustomers, fetchInvoices, createPayment } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

async function fetchPayments() {
  const response = await apiRequest("GET", "/api/payments");
  return response.json();
}

export default function Payments() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: payments, isLoading } = useQuery({
    queryKey: ["/api/payments"],
    queryFn: fetchPayments,
  });

  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
    queryFn: fetchCustomers,
  });

  const { data: invoices } = useQuery({
    queryKey: ["/api/invoices"],
    queryFn: fetchInvoices,
  });

  const createPaymentMutation = useMutation({
    mutationFn: createPayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
      queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
      queryClient.invalidateQueries({ queryKey: ["/api/customers"] });
      setIsDialogOpen(false);
      setSelectedCustomer(null);
      setSelectedInvoice(null);
      toast({
        title: "Success",
        description: "Payment recorded successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to record payment",
        variant: "destructive",
      });
    },
  });

  const filteredPayments = payments?.filter((payment: any) => {
    const customer = customers?.find((c: any) => c.id === payment.customerId);
    return customer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           payment.referenceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           payment.paymentMode.toLowerCase().includes(searchTerm.toLowerCase());
  }) || [];

  const customerInvoices = selectedCustomer 
    ? invoices?.filter((invoice: any) => 
        invoice.customerId === selectedCustomer.id && 
        parseFloat(invoice.balanceAmount) > 0
      ) 
    : [];

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = {
      customerId: parseInt(formData.get("customerId") as string),
      invoiceId: formData.get("invoiceId") ? parseInt(formData.get("invoiceId") as string) : null,
      paymentDate: formData.get("paymentDate"),
      amount: formData.get("amount"),
      paymentMode: formData.get("paymentMode"),
      referenceNumber: formData.get("referenceNumber"),
      notes: formData.get("notes"),
    };
    createPaymentMutation.mutate(data);
  };

  const getPaymentModeIcon = (mode: string) => {
    switch (mode.toLowerCase()) {
      case "cash":
        return "💵";
      case "bank":
      case "rtgs":
      case "neft":
        return "🏦";
      case "upi":
        return "📱";
      case "cheque":
        return "📝";
      default:
        return "💳";
    }
  };

  const getPaymentModeColor = (mode: string) => {
    switch (mode.toLowerCase()) {
      case "cash":
        return "bg-green-100 text-green-800";
      case "bank":
      case "rtgs":
      case "neft":
        return "bg-blue-100 text-blue-800";
      case "upi":
        return "bg-purple-100 text-purple-800";
      case "cheque":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-neutral-100 text-neutral-800";
    }
  };

  // Calculate payment statistics
  const today = new Date().toDateString();
  const todaysPayments = payments?.filter((p: any) => 
    new Date(p.paymentDate).toDateString() === today
  ) || [];
  const todaysTotal = todaysPayments.reduce((sum: number, p: any) => sum + parseFloat(p.amount), 0);

  const thisMonth = new Date().getMonth();
  const thisYear = new Date().getFullYear();
  const monthlyPayments = payments?.filter((p: any) => {
    const paymentDate = new Date(p.paymentDate);
    return paymentDate.getMonth() === thisMonth && paymentDate.getFullYear() === thisYear;
  }) || [];
  const monthlyTotal = monthlyPayments.reduce((sum: number, p: any) => sum + parseFloat(p.amount), 0);

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Payment Management" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Payment Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">Today's Payments</p>
                    <p className="text-2xl font-bold text-neutral-900">
                      ₹{todaysTotal.toLocaleString("en-IN")}
                    </p>
                    <p className="text-sm text-neutral-500">{todaysPayments.length} transactions</p>
                  </div>
                  <div className="p-3 rounded-full bg-green-100">
                    <DollarSign className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">This Month</p>
                    <p className="text-2xl font-bold text-neutral-900">
                      ₹{monthlyTotal.toLocaleString("en-IN")}
                    </p>
                    <p className="text-sm text-neutral-500">{monthlyPayments.length} transactions</p>
                  </div>
                  <div className="p-3 rounded-full bg-blue-100">
                    <Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">Total Outstanding</p>
                    <p className="text-2xl font-bold text-neutral-900">
                      ₹{(customers?.reduce((sum: number, c: any) => sum + parseFloat(c.outstandingAmount || "0"), 0) || 0).toLocaleString("en-IN")}
                    </p>
                    <p className="text-sm text-neutral-500">Across all customers</p>
                  </div>
                  <div className="p-3 rounded-full bg-red-100">
                    <Receipt className="w-6 h-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">Active Customers</p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {customers?.filter((c: any) => parseFloat(c.outstandingAmount || "0") > 0).length || 0}
                    </p>
                    <p className="text-sm text-neutral-500">With pending payments</p>
                  </div>
                  <div className="p-3 rounded-full bg-[hsl(154,50%,90%)]">
                    <User className="w-6 h-6 text-[hsl(154,50%,20%)]" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Header Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                <Input
                  placeholder="Search payments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]">
                  <Plus className="w-4 h-4 mr-2" />
                  Record Payment
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Record New Payment</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="customerId">Customer</Label>
                      <Select 
                        name="customerId" 
                        required
                        onValueChange={(value) => {
                          const customer = customers?.find((c: any) => c.id.toString() === value);
                          setSelectedCustomer(customer);
                          setSelectedInvoice(null);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers?.map((customer: any) => (
                            <SelectItem key={customer.id} value={customer.id.toString()}>
                              {customer.name} - ₹{parseInt(customer.outstandingAmount || "0").toLocaleString("en-IN")} outstanding
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="invoiceId">Invoice (Optional)</Label>
                      <Select name="invoiceId" onValueChange={(value) => {
                        if (value === "general") {
                          setSelectedInvoice(null);
                        } else {
                          const invoice = customerInvoices.find((i: any) => i.id.toString() === value);
                          setSelectedInvoice(invoice);
                        }
                      }}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select invoice" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">General Payment</SelectItem>
                          {customerInvoices.map((invoice: any) => (
                            <SelectItem key={invoice.id} value={invoice.id.toString()}>
                              {invoice.invoiceNumber} - ₹{parseInt(invoice.balanceAmount).toLocaleString("en-IN")} due
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {selectedCustomer && (
                    <div className="p-3 bg-neutral-50 rounded-lg">
                      <p className="font-medium text-neutral-800">{selectedCustomer.name}</p>
                      <p className="text-sm text-neutral-600">
                        Outstanding: ₹{parseInt(selectedCustomer.outstandingAmount || "0").toLocaleString("en-IN")}
                      </p>
                      {selectedInvoice && (
                        <p className="text-sm text-neutral-600">
                          Invoice {selectedInvoice.invoiceNumber} - Balance: ₹{parseInt(selectedInvoice.balanceAmount).toLocaleString("en-IN")}
                        </p>
                      )}
                    </div>
                  )}

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="amount">Amount (₹)</Label>
                      <Input id="amount" name="amount" type="number" step="0.01" required />
                    </div>
                    <div>
                      <Label htmlFor="paymentDate">Payment Date</Label>
                      <Input id="paymentDate" name="paymentDate" type="date" defaultValue={new Date().toISOString().split('T')[0]} required />
                    </div>
                    <div>
                      <Label htmlFor="paymentMode">Payment Mode</Label>
                      <Select name="paymentMode" required>
                        <SelectTrigger>
                          <SelectValue placeholder="Select mode" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="bank">Bank Transfer</SelectItem>
                          <SelectItem value="upi">UPI</SelectItem>
                          <SelectItem value="rtgs">RTGS</SelectItem>
                          <SelectItem value="neft">NEFT</SelectItem>
                          <SelectItem value="cheque">Cheque</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="referenceNumber">Reference Number</Label>
                      <Input id="referenceNumber" name="referenceNumber" placeholder="Transaction ID, Cheque No, etc." />
                    </div>
                    <div>
                      <Label htmlFor="notes">Notes</Label>
                      <Textarea id="notes" name="notes" rows={3} placeholder="Additional notes..." />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={createPaymentMutation.isPending}>
                      {createPaymentMutation.isPending ? "Recording..." : "Record Payment"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Payment Records */}
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-neutral-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-neutral-200 rounded w-1/2 mb-4"></div>
                      <div className="h-3 bg-neutral-200 rounded w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredPayments.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <CreditCard className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-neutral-700 mb-2">No payments found</h3>
                <p className="text-neutral-500 mb-4">
                  {searchTerm ? "No payments match your search criteria." : "Start by recording your first payment."}
                </p>
                <Button 
                  onClick={() => setIsDialogOpen(true)}
                  className="bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Record Payment
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredPayments.map((payment: any) => {
                const customer = customers?.find((c: any) => c.id === payment.customerId);
                const invoice = payment.invoiceId ? invoices?.find((i: any) => i.id === payment.invoiceId) : null;

                return (
                  <Card key={payment.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-xl">{getPaymentModeIcon(payment.paymentMode)}</span>
                              <h3 className="text-lg font-semibold text-neutral-900">
                                ₹{parseInt(payment.amount).toLocaleString("en-IN")}
                              </h3>
                            </div>
                            <Badge className={getPaymentModeColor(payment.paymentMode)}>
                              {payment.paymentMode.toUpperCase()}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-neutral-600">Customer</p>
                              <p className="font-medium">{customer?.name || `Customer #${payment.customerId}`}</p>
                            </div>
                            <div>
                              <p className="text-neutral-600">Payment Date</p>
                              <p className="font-medium">
                                {new Date(payment.paymentDate).toLocaleDateString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-neutral-600">Invoice</p>
                              <p className="font-medium">
                                {invoice ? invoice.invoiceNumber : "General Payment"}
                              </p>
                            </div>
                            <div>
                              <p className="text-neutral-600">Reference</p>
                              <p className="font-medium">
                                {payment.referenceNumber || "-"}
                              </p>
                            </div>
                          </div>
                          
                          {payment.notes && (
                            <div className="mt-3">
                              <p className="text-sm text-neutral-600">Notes: {payment.notes}</p>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Receipt className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </main>
      </div>
    </div>
  );
}