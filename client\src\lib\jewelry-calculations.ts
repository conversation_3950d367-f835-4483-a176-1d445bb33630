// Jewelry Calculation Utilities
// Based on the universal formula set for Gold, Silver, and Platinum
//
// 🧾 Industry Standard Purity Reference Table
// =============================================
// Metal    | Purity Code | Purity % | Common Name
// ---------|-------------|----------|------------------
// Gold     | 999         | 99.9%    | 24K Pure Gold
// Gold     | 916         | 96.0%    | 22K Standard (Industry Standard)
// Gold     | 750         | 75.0%    | 18K Light Jewelry
// Gold     | 585         | 58.5%    | 14K Budget Jewelry
// ---------|-------------|----------|------------------
// Silver   | 999         | 99.9%    | Pure Silver
// Silver   | 925         | 92.5%    | Sterling Silver
// Silver   | 800         | 80.0%    | Standard Silver
// ---------|-------------|----------|------------------
// Platinum | 950         | 95.0%    | Standard Jewelry
// Platinum | 900         | 90.0%    | Alternative Grade
// Platinum | 850         | 85.0%    | Budget Grade
// =============================================

export interface JewelryItem {
  grossWeight: number;
  stoneWeight?: number;
  purity: number; // Purity percentage (e.g., 91.6 for 22K gold)
  ratePerGram: number;
  wastagePercentage?: number;
  makingChargesRate?: number; // Making charges per gram
  stoneAmount?: number; // Stone price
}

export interface CalculationResult {
  netWeight: number;
  fineWeight: number;
  metalValue: number;
  wastageWeight?: number;
  makingCharges: number;
  stoneAmount: number;
  totalBeforeTax: number;
  cgst: number;
  sgst: number;
  totalGst: number;
  grandTotal: number;
}

// Purity reference table - Industry Standard Percentages
export const PURITY_REFERENCE = {
  gold: {
    "999": { percentage: 99.9, label: "24K Pure Gold" },
    "916": { percentage: 96.0, label: "22K Standard Jewelry" }, // Industry standard: 96% for 22K
    "750": { percentage: 75.0, label: "18K Light Jewelry" },
    "585": { percentage: 58.5, label: "14K Budget Jewelry" }
  },
  silver: {
    "999": { percentage: 99.9, label: "Pure Silver" },
    "925": { percentage: 92.5, label: "Sterling Silver" },
    "800": { percentage: 80.0, label: "Standard Silver" }
  },
  platinum: {
    "950": { percentage: 95.0, label: "Standard Jewelry Platinum" },
    "900": { percentage: 90.0, label: "Alternative Platinum" },
    "850": { percentage: 85.0, label: "Budget Platinum" }
  }
};

// Get purity percentage from purity code
export function getPurityPercentage(metalType: string, purityCode: string): number {
  const metalPurities = PURITY_REFERENCE[metalType as keyof typeof PURITY_REFERENCE];
  if (!metalPurities) return 0;

  const purity = metalPurities[purityCode as keyof typeof metalPurities] as { percentage: number; label: string } | undefined;
  return purity ? purity.percentage : 0;
}

// Calculate jewelry item values using the universal formula
export function calculateJewelryItem(item: JewelryItem): CalculationResult {
  // Step 1: Calculate Net Weight
  const netWeight = item.grossWeight - (item.stoneWeight || 0);
  
  // Step 2: Calculate Fine Weight
  const fineWeight = netWeight * (item.purity / 100);
  
  // Step 3: Calculate Metal Value
  const metalValue = fineWeight * item.ratePerGram;
  
  // Step 4: Calculate Wastage (optional)
  const wastageWeight = item.wastagePercentage 
    ? netWeight * (item.wastagePercentage / 100) 
    : 0;
  
  // Step 5: Calculate Making Charges
  const makingCharges = item.makingChargesRate 
    ? netWeight * item.makingChargesRate 
    : 0;
  
  // Step 6: Stone Amount
  const stoneAmount = item.stoneAmount || 0;
  
  // Step 7: Total Before Tax
  const totalBeforeTax = metalValue + makingCharges + stoneAmount;
  
  // Step 8: Calculate GST (3% total = 1.5% CGST + 1.5% SGST)
  const cgst = totalBeforeTax * 0.015; // 1.5%
  const sgst = totalBeforeTax * 0.015; // 1.5%
  const totalGst = cgst + sgst;
  
  // Step 9: Grand Total
  const grandTotal = totalBeforeTax + totalGst;
  
  return {
    netWeight,
    fineWeight,
    metalValue,
    wastageWeight,
    makingCharges,
    stoneAmount,
    totalBeforeTax,
    cgst,
    sgst,
    totalGst,
    grandTotal
  };
}

// Calculate multiple items and return combined totals
export function calculateMultipleItems(items: JewelryItem[]): {
  items: CalculationResult[];
  subtotal: number;
  totalCgst: number;
  totalSgst: number;
  totalGst: number;
  grandTotal: number;
} {
  const calculatedItems = items.map(item => calculateJewelryItem(item));
  
  const subtotal = calculatedItems.reduce((sum, item) => sum + item.totalBeforeTax, 0);
  const totalCgst = calculatedItems.reduce((sum, item) => sum + item.cgst, 0);
  const totalSgst = calculatedItems.reduce((sum, item) => sum + item.sgst, 0);
  const totalGst = totalCgst + totalSgst;
  const grandTotal = subtotal + totalGst;
  
  return {
    items: calculatedItems,
    subtotal,
    totalCgst,
    totalSgst,
    totalGst,
    grandTotal
  };
}

// Format currency for display
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3
  }).format(amount);
}

// Format weight for display
export function formatWeight(weight: number): string {
  return `${weight.toFixed(3)}g`;
}

// Format percentage for display
export function formatPercentage(percentage: number): string {
  return `${percentage.toFixed(2)}%`;
}

// Validate jewelry item data
export function validateJewelryItem(item: Partial<JewelryItem>): string[] {
  const errors: string[] = [];
  
  if (!item.grossWeight || item.grossWeight <= 0) {
    errors.push("Gross weight must be greater than 0");
  }
  
  if (item.stoneWeight && item.stoneWeight < 0) {
    errors.push("Stone weight cannot be negative");
  }
  
  if (item.stoneWeight && item.grossWeight && item.stoneWeight >= item.grossWeight) {
    errors.push("Stone weight cannot be greater than or equal to gross weight");
  }
  
  if (!item.purity || item.purity <= 0 || item.purity > 100) {
    errors.push("Purity must be between 0 and 100");
  }
  
  if (!item.ratePerGram || item.ratePerGram <= 0) {
    errors.push("Rate per gram must be greater than 0");
  }
  
  if (item.wastagePercentage && (item.wastagePercentage < 0 || item.wastagePercentage > 100)) {
    errors.push("Wastage percentage must be between 0 and 100");
  }
  
  if (item.makingChargesRate && item.makingChargesRate < 0) {
    errors.push("Making charges rate cannot be negative");
  }
  
  if (item.stoneAmount && item.stoneAmount < 0) {
    errors.push("Stone amount cannot be negative");
  }
  
  return errors;
}

// Example calculation (as per the provided formula)
export function getExampleCalculation() {
  const exampleItem: JewelryItem = {
    grossWeight: 16.210,
    stoneWeight: 0.345,
    purity: 96.0, // 22K Gold - Industry Standard
    ratePerGram: 10112,
    wastagePercentage: 1,
    makingChargesRate: 150,
    stoneAmount: 5600
  };

  return calculateJewelryItem(exampleItem);
}

// Test calculation to verify against your example
export function testCalculationExample() {
  const result = getExampleCalculation();

  console.log("🧮 Jewelry Calculation Test - 22K Gold Ring");
  console.log("============================================");
  console.log(`Gross Weight: ${formatWeight(16.210)}`);
  console.log(`Stone Weight: ${formatWeight(0.345)}`);
  console.log(`Net Weight: ${formatWeight(result.netWeight)}`);
  console.log(`Purity: 96% (22K Gold - Industry Standard)`);
  console.log(`Fine Weight: ${formatWeight(result.fineWeight)}`);
  console.log(`Rate per Gram: ${formatCurrency(10112)}`);
  console.log(`Gold Value: ${formatCurrency(result.metalValue)}`);
  console.log(`Making Charges: ${formatCurrency(result.makingCharges)}`);
  console.log(`Stone Amount: ${formatCurrency(5600)}`);
  console.log(`Total Before Tax: ${formatCurrency(result.totalBeforeTax)}`);
  console.log(`CGST (1.5%): ${formatCurrency(result.cgst)}`);
  console.log(`SGST (1.5%): ${formatCurrency(result.sgst)}`);
  console.log(`Grand Total: ${formatCurrency(result.grandTotal)}`);
  console.log("============================================");

  return result;
}
