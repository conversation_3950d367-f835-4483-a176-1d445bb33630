import { db } from "./db";
import { users } from "@shared/schema";
import bcrypt from "bcryptjs";
import { eq } from "drizzle-orm";

async function initializeUsers() {
  console.log("Initializing default users...");

  const defaultUsers = [
    {
      username: "admin",
      password: "admin123",
      name: "System Administrator",
      role: "admin" as const
    },
    {
      username: "manager",
      password: "manager123", 
      name: "Store Manager",
      role: "manager" as const
    },
    {
      username: "user",
      password: "user123",
      name: "Store User",
      role: "user" as const
    }
  ];

  for (const userData of defaultUsers) {
    try {
      // Check if user already exists
      const [existingUser] = await db.select().from(users).where(eq(users.username, userData.username));
      
      if (existingUser) {
        console.log(`User '${userData.username}' already exists, skipping...`);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Create user
      await db.insert(users).values({
        username: userData.username,
        password: hashedPassword,
        name: userData.name,
        role: userData.role
      });

      console.log(`✅ Created user: ${userData.username} (${userData.role})`);
    } catch (error) {
      console.error(`❌ Error creating user '${userData.username}':`, error);
    }
  }

  console.log("User initialization complete!");
}

// Run the initialization
initializeUsers()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Failed to initialize users:", error);
    process.exit(1);
  });

export { initializeUsers };
