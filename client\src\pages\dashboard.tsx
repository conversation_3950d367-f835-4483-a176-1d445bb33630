import { useQuery } from "@tanstack/react-query";
import { DollarSign, Package, Clock, AlertTriangle, Plus, CreditCard, BarChart3, FolderSync, User, Shield, Users } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import StatsCard from "@/components/ui/stats-card";
import BillingModal from "@/components/billing/billing-modal";
import { fetchDashboardStats, fetchInvoices, fetchCustomers, fetchMetalRates } from "@/lib/api";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth-context";
import { Badge } from "@/components/ui/badge";

export default function Dashboard() {
  const [isBillingModalOpen, setIsBillingModalOpen] = useState(false);
  const { user, hasPermission } = useAuth();

  const { data: stats } = useQuery({
    queryKey: ["/api/dashboard/stats"],
    queryFn: fetchDashboardStats,
  });

  const { data: invoices } = useQuery({
    queryKey: ["/api/invoices"],
    queryFn: fetchInvoices,
  });

  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
    queryFn: fetchCustomers,
  });

  const { data: metalRates } = useQuery({
    queryKey: ["/api/metal-rates/current"],
    queryFn: fetchMetalRates,
  });

  // Debug logging
  console.log("Dashboard - Metal Rates Data:", metalRates);

  const recentInvoices = invoices?.slice(0, 3) || [];
  const topCustomers = customers?.slice(0, 3) || [];

  // Helper function to get current rate for a specific metal and purity
  const getCurrentRate = (metalType: string, purity: string) => {
    return metalRates?.find((rate: any) =>
      rate.metalType === metalType && rate.purity === purity
    );
  };

  // Get specific rates for dashboard display
  const goldRate24K = getCurrentRate("gold", "999");
  const goldRate22K = getCurrentRate("gold", "916");
  const silverRate925 = getCurrentRate("silver", "925");
  const silverRate999 = getCurrentRate("silver", "999");

  return (
    <div className="flex h-screen bg-neutral-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title="Dashboard" />
        
        <main className="flex-1 overflow-auto p-6">
          {/* User Welcome Section */}
          <div className="mb-6 bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-[hsl(154,50%,20%)] rounded-full flex items-center justify-center">
                  {user?.role === "admin" ? (
                    <Shield className="w-6 h-6 text-white" />
                  ) : user?.role === "manager" ? (
                    <Users className="w-6 h-6 text-white" />
                  ) : (
                    <User className="w-6 h-6 text-white" />
                  )}
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-neutral-800">
                    Welcome back, {user?.name}!
                  </h1>
                  <p className="text-neutral-600">
                    {user?.role === "admin"
                      ? "You have full system access and can manage all aspects of the jewelry business."
                      : user?.role === "manager"
                      ? "You have advanced access to manage operations, rates, and settings."
                      : "You have access to view data and create invoices for customers."
                    }
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge
                  className={
                    user?.role === "admin"
                      ? "bg-red-100 text-red-800 border-red-200"
                      : user?.role === "manager"
                      ? "bg-blue-100 text-blue-800 border-blue-200"
                      : "bg-green-100 text-green-800 border-green-200"
                  }
                >
                  {user?.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'User'} Access
                </Badge>
                <p className="text-xs text-neutral-500 mt-1">
                  Logged in as @{user?.username}
                </p>
              </div>
            </div>
          </div>

          {/* Stats Cards - Role-based visibility */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Sales data - visible to all users */}
            <StatsCard
              title="Today's Sales"
              value={`₹${parseInt(stats?.todaysSales || "0").toLocaleString("en-IN")}`}
              subtitle={user?.role === "user" ? "View only" : ""}
              icon={<DollarSign className="w-6 h-6" />}
              trend={hasPermission("reports.view") ? { value: "+12.5% from yesterday", isPositive: true } : undefined}
              iconBgColor="bg-green-100"
              iconColor="text-green-600"
            />

            {/* Inventory data - visible to all users */}
            <StatsCard
              title="Total Inventory Value"
              value={hasPermission("inventory.edit")
                ? `₹${parseInt(stats?.inventoryValue || "0").toLocaleString("en-IN")}`
                : "₹***,***"
              }
              subtitle={`${stats?.inventoryCount || 0} items in stock`}
              icon={<Package className="w-6 h-6" />}
              iconBgColor="bg-[hsl(38,92%,50%)] bg-opacity-10"
              iconColor="text-[hsl(38,92%,50%)]"
            />

            {/* Payment data - restricted for basic users */}
            <StatsCard
              title="Pending Payments"
              value={hasPermission("payments.edit")
                ? `₹${parseInt(stats?.pendingPayments || "0").toLocaleString("en-IN")}`
                : "₹***,***"
              }
              subtitle={hasPermission("payments.edit")
                ? `${stats?.overdueCount || 0} overdue accounts`
                : "Limited access"
              }
              icon={<Clock className="w-6 h-6" />}
              iconBgColor="bg-yellow-100"
              iconColor="text-yellow-600"
            />

            {/* Stock alerts - visible based on permissions */}
            <StatsCard
              title="Low Stock Items"
              value={hasPermission("inventory.view")
                ? (stats?.lowStockCount?.toString() || "0")
                : "***"
              }
              subtitle={hasPermission("inventory.edit")
                ? "Requires immediate attention"
                : "Contact manager for details"
              }
              icon={<AlertTriangle className="w-6 h-6" />}
              iconBgColor="bg-red-100"
              iconColor="text-red-600"
            />
          </div>

          {/* Role-based Quick Actions & Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Quick Actions - Role-based */}
            <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 mb-4">
                Quick Actions
                <Badge className="ml-2 text-xs bg-neutral-100 text-neutral-600">
                  {user?.role} access
                </Badge>
              </h3>
              <div className="space-y-3">
                {/* Invoice creation - available to all users */}
                <Button
                  onClick={() => setIsBillingModalOpen(true)}
                  className="w-full bg-[hsl(154,50%,20%)] hover:bg-[hsl(154,46%,35%)]"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  New Invoice
                </Button>

                {/* Inventory management - Manager+ only */}
                {hasPermission("inventory.create") ? (
                  <Button variant="outline" className="w-full bg-[hsl(38,92%,50%)] text-white hover:bg-[hsl(45,93%,58%)]">
                    <Package className="w-4 h-4 mr-2" />
                    Add Inventory
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full" disabled>
                    <Package className="w-4 h-4 mr-2" />
                    Add Inventory (Manager+)
                  </Button>
                )}

                {/* Payment recording - Manager+ only */}
                {hasPermission("payments.create") ? (
                  <Button variant="outline" className="w-full bg-neutral-600 text-white hover:bg-neutral-700">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Record Payment
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full" disabled>
                    <CreditCard className="w-4 h-4 mr-2" />
                    Record Payment (Manager+)
                  </Button>
                )}
              </div>
            </div>

            {/* Rate Updates - Role-based access */}
            <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-800">
                  Metal Rates
                  {!hasPermission("rates.edit") && (
                    <Badge className="ml-2 text-xs bg-yellow-100 text-yellow-600">
                      View Only
                    </Badge>
                  )}
                </h3>
                {hasPermission("rates.edit") ? (
                  <a
                    href="/rates"
                    className="text-sm text-[hsl(154,50%,20%)] hover:text-[hsl(154,46%,35%)] transition-colors"
                  >
                    <FolderSync className="w-4 h-4 mr-1 inline" />
                    Update
                  </a>
                ) : (
                  <span className="text-sm text-neutral-400">
                    <FolderSync className="w-4 h-4 mr-1 inline" />
                    Manager+ Only
                  </span>
                )}
              </div>
              <div className="space-y-4">
                {/* Gold 24K Rate */}
                {goldRate24K && (
                  <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-[hsl(38,92%,50%)] rounded-full flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-neutral-800">Gold 24K (999)</p>
                        <p className="text-sm text-neutral-600">per gram</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-neutral-800">
                        ₹{parseFloat(goldRate24K.ratePerGram).toLocaleString("en-IN")}
                      </p>
                      <p className="text-xs text-neutral-500">
                        {new Date(goldRate24K.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Gold 22K Rate */}
                {goldRate22K && (
                  <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-[hsl(38,92%,50%)] rounded-full flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-neutral-800">Gold 22K (916)</p>
                        <p className="text-sm text-neutral-600">per gram</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-neutral-800">
                        ₹{parseFloat(goldRate22K.ratePerGram).toLocaleString("en-IN")}
                      </p>
                      <p className="text-xs text-neutral-500">
                        {new Date(goldRate22K.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Silver Rate */}
                {(silverRate925 || silverRate999) && (
                  <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-neutral-400 rounded-full flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-neutral-800">
                          Silver ({silverRate925 ? '925' : '999'})
                        </p>
                        <p className="text-sm text-neutral-600">per gram</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-neutral-800">
                        ₹{parseFloat((silverRate925 || silverRate999).ratePerGram).toLocaleString("en-IN")}
                      </p>
                      <p className="text-xs text-neutral-500">
                        {new Date((silverRate925 || silverRate999).createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {/* No rates available message */}
                {!goldRate24K && !goldRate22K && !silverRate925 && !silverRate999 && (
                  <div className="text-center py-8">
                    <p className="text-neutral-500 mb-2">No current rates available</p>
                    <a
                      href="/rates"
                      className="text-[hsl(154,50%,20%)] hover:text-[hsl(154,46%,35%)] text-sm font-medium"
                    >
                      Add metal rates →
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Notifications */}
            <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
              <h3 className="text-lg font-semibold text-neutral-800 mb-4">Recent Notifications</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mt-1" />
                  <div>
                    <p className="text-sm font-medium text-neutral-800">Low Stock Alert</p>
                    <p className="text-xs text-neutral-600 mt-1">Gold chains (22K) - Only 5 pieces remaining</p>
                    <p className="text-xs text-neutral-500 mt-1">2 hours ago</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                  <div className="w-4 h-4 bg-green-500 rounded-full mt-1"></div>
                  <div>
                    <p className="text-sm font-medium text-neutral-800">Payment Received</p>
                    <p className="text-xs text-neutral-600 mt-1">₹45,000 from Kumar Jewellers</p>
                    <p className="text-xs text-neutral-500 mt-1">4 hours ago</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                  <div className="w-4 h-4 bg-blue-500 rounded-full mt-1"></div>
                  <div>
                    <p className="text-sm font-medium text-neutral-800">Rate Update</p>
                    <p className="text-xs text-neutral-600 mt-1">Gold rates updated successfully</p>
                    <p className="text-xs text-neutral-500 mt-1">6 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* User Activity Summary */}
          <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 mb-4">
              Your Activity Summary
              <Badge className="ml-2 text-xs bg-blue-100 text-blue-600">
                {user?.role} dashboard
              </Badge>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Role-specific activity cards */}
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {user?.role === "admin" ? "Full" : user?.role === "manager" ? "Advanced" : "Basic"}
                </div>
                <div className="text-sm text-green-700">Access Level</div>
              </div>

              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {hasPermission("invoices.create") ? "✓" : "✗"}
                </div>
                <div className="text-sm text-blue-700">Invoice Creation</div>
              </div>

              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {hasPermission("settings.edit") ? "✓" : "✗"}
                </div>
                <div className="text-sm text-purple-700">Settings Access</div>
              </div>
            </div>

            {/* Role-specific tips */}
            <div className="mt-4 p-3 bg-neutral-50 rounded-lg">
              <p className="text-sm text-neutral-600">
                <strong>💡 Tip for {user?.role}s:</strong>{" "}
                {user?.role === "admin"
                  ? "You can manage users, update all settings, and access all financial data. Check the User Management section for creating new accounts."
                  : user?.role === "manager"
                  ? "You can update metal rates, manage inventory, and access most reports. Use the Quick Actions to streamline your workflow."
                  : "You can create invoices and view most data. Contact your manager for inventory updates or rate changes."
                }
              </p>
            </div>
          </div>

          {/* Recent Transactions & Top Customers */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Transactions */}
            <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-800">Recent Transactions</h3>
                <a href="#" className="text-sm text-[hsl(154,50%,20%)] hover:text-[hsl(154,46%,35%)]">View All</a>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-neutral-200">
                      <th className="text-left py-2 text-xs font-semibold text-neutral-600 uppercase">Invoice</th>
                      <th className="text-left py-2 text-xs font-semibold text-neutral-600 uppercase">Customer</th>
                      <th className="text-right py-2 text-xs font-semibold text-neutral-600 uppercase">Amount</th>
                      <th className="text-right py-2 text-xs font-semibold text-neutral-600 uppercase">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentInvoices.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="py-4 text-center text-neutral-500">No recent transactions</td>
                      </tr>
                    ) : (
                      recentInvoices.map((invoice: any) => (
                        <tr key={invoice.id} className="border-b border-neutral-100">
                          <td className="py-3">
                            <p className="font-medium text-neutral-800">{invoice.invoiceNumber}</p>
                            <p className="text-xs text-neutral-500">{new Date(invoice.invoiceDate).toLocaleDateString()}</p>
                          </td>
                          <td className="py-3">
                            <p className="font-medium text-neutral-800">Customer #{invoice.customerId}</p>
                          </td>
                          <td className="py-3 text-right">
                            <p className="font-semibold text-neutral-800">₹{parseInt(invoice.totalAmount).toLocaleString("en-IN")}</p>
                          </td>
                          <td className="py-3 text-right">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              invoice.status === 'paid' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.status}
                            </span>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Top Customers */}
            <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-800">Top Customers (This Month)</h3>
                <a href="#" className="text-sm text-[hsl(154,50%,20%)] hover:text-[hsl(154,46%,35%)]">View All</a>
              </div>
              <div className="space-y-4">
                {topCustomers.length === 0 ? (
                  <p className="text-center text-neutral-500">No customers found</p>
                ) : (
                  topCustomers.map((customer: any) => (
                    <div key={customer.id} className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-[hsl(154,50%,20%)] rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold text-sm">
                            {customer.name?.substring(0, 2).toUpperCase() || "NA"}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-neutral-800">{customer.name}</p>
                          <p className="text-sm text-neutral-600">{customer.gstin || "No GSTIN"}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-neutral-800">₹{parseInt(customer.outstandingAmount || "0").toLocaleString("en-IN")}</p>
                        <p className="text-xs text-neutral-500">Outstanding</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </main>
      </div>

      <BillingModal 
        isOpen={isBillingModalOpen} 
        onClose={() => setIsBillingModalOpen(false)} 
      />
    </div>
  );
}
