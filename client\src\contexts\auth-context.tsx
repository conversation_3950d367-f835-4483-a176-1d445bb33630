import React, { createContext, useContext, useEffect, useState } from "react";
import { apiRequest } from "@/lib/api";

export interface User {
  id: number;
  username: string;
  name: string;
  role: "admin" | "manager" | "user";
  createdAt: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (token: string, user: User) => void;
  logout: () => void;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasRole: (role: string | string[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Role-based permissions
const ROLE_PERMISSIONS = {
  admin: [
    "dashboard.view",
    "inventory.view", "inventory.create", "inventory.edit", "inventory.delete",
    "customers.view", "customers.create", "customers.edit", "customers.delete",
    "suppliers.view", "suppliers.create", "suppliers.edit", "suppliers.delete",
    "invoices.view", "invoices.create", "invoices.edit", "invoices.delete",
    "payments.view", "payments.create", "payments.edit", "payments.delete",
    "rates.view", "rates.create", "rates.edit", "rates.delete",
    "procurement.view", "procurement.create", "procurement.edit", "procurement.delete",
    "reports.view", "reports.export",
    "settings.view", "settings.edit",
    "users.view", "users.create", "users.edit", "users.delete"
  ],
  manager: [
    "dashboard.view",
    "inventory.view", "inventory.create", "inventory.edit",
    "customers.view", "customers.create", "customers.edit",
    "suppliers.view", "suppliers.create", "suppliers.edit",
    "invoices.view", "invoices.create", "invoices.edit",
    "payments.view", "payments.create", "payments.edit",
    "rates.view", "rates.create", "rates.edit",
    "procurement.view", "procurement.create", "procurement.edit",
    "reports.view", "reports.export",
    "settings.view"
  ],
  user: [
    "dashboard.view",
    "inventory.view",
    "customers.view",
    "suppliers.view",
    "invoices.view", "invoices.create",
    "payments.view",
    "rates.view",
    "procurement.view",
    "reports.view"
  ]
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const storedToken = localStorage.getItem("authToken");
    const storedUser = localStorage.getItem("user");

    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setToken(storedToken);
        setUser(parsedUser);

        // Verify token is still valid
        verifyToken(storedToken).catch(() => {
          // Token is invalid, clear auth state
          logout();
        });
      } catch (error) {
        console.error("Error parsing stored user data:", error);
        logout();
      }
    }

    setIsLoading(false);
  }, []);

  const verifyToken = async (token: string) => {
    try {
      const response = await apiRequest("GET", "/api/auth/verify", {}, {
        Authorization: `Bearer ${token}`
      });
      
      if (!response.ok) {
        throw new Error("Token verification failed");
      }
      
      return response.json();
    } catch (error) {
      throw error;
    }
  };

  const login = (newToken: string, newUser: User) => {
    setToken(newToken);
    setUser(newUser);
    localStorage.setItem("authToken", newToken);
    localStorage.setItem("user", JSON.stringify(newUser));
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem("authToken");
    localStorage.removeItem("user");
  };

  const hasRole = (role: string | string[]): boolean => {
    if (!user) return false;
    
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    
    return user.role === role;
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes(permission);
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    isAuthenticated: !!user && !!token,
    isLoading,
    hasRole,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string | string[]
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, hasRole, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[hsl(154,50%,20%)] mx-auto mb-4"></div>
            <p className="text-neutral-600">Loading...</p>
          </div>
        </div>
      );
    }

    if (!isAuthenticated) {
      // Use setTimeout to avoid React state update issues
      setTimeout(() => {
        window.location.href = "/login";
      }, 0);
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-neutral-600">Redirecting to login...</p>
          </div>
        </div>
      );
    }

    if (requiredRole && !hasRole(requiredRole)) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-neutral-800 mb-2">Access Denied</h1>
            <p className="text-neutral-600">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
